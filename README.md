# Git Release Log Maven Plugin

A Maven plugin that generates a Git release log showing commits organized by tags. This plugin retrieves Git history from the current repository and displays commits grouped by their associated tags, providing a clear view of what changes were included in each release.

## Features

- 📋 **Tag-based Organization**: Groups commits by Git tags (releases)
- 🎯 **Commit Details**: Shows commit hash, message, author, and timestamp
- 🌿 **Branch Extraction**: Extracts branch names from Bitbucket merge messages
- 🏷️ **Smart Categorization**: Automatically categorizes commits (feature, bugfix, task, cid, etc.)
- 🔍 **Fuzzy Matching**: Uses Levenshtein distance for flexible keyword matching
- 📊 **Multiple Output Formats**: Console, text file, CSV, and category statistics
- 📈 **Summary Statistics**: Displays total tags, commits, averages, and category breakdown
- 🔧 **Highly Configurable**: Customizable categories, keywords, and matching thresholds
- 🚀 **Easy Integration**: Simple Maven goal execution

## Usage

### Basic Usage

Run the plugin in any Git repository with <PERSON>ven:

```bash
mvn com.nikolascharalambidis:git-rellog-maven-plugin:1.0-SNAPSHOT:generate
```

### Plugin Configuration

Add the plugin to your `pom.xml`:

```xml
<build>
    <plugins>
        <plugin>
            <groupId>com.nikolascharalambidis</groupId>
            <artifactId>git-rellog-maven-plugin</artifactId>
            <version>1.0-SNAPSHOT</version>
            <configuration>
                <!-- Optional: Specify repository directory (defaults to ${project.basedir}) -->
                <repositoryDirectory>${project.basedir}</repositoryDirectory>
                
                <!-- Optional: Include summary at the end (default: true) -->
                <includeSummary>true</includeSummary>
                
                <!-- Optional: Skip plugin execution (default: false) -->
                <skip>false</skip>
                
                <!-- Optional: Maximum number of tags to process (default: -1 for no limit) -->
                <maxTags>10</maxTags>

                <!-- Optional: Output directory for files (default: ${project.basedir}) -->
                <outputDirectory>${project.basedir}/reports</outputDirectory>

                <!-- Optional: Save to files (default: true) -->
                <saveToFiles>true</saveToFiles>

                <!-- Optional: Levenshtein distance threshold for fuzzy matching (default: 2) -->
                <levenshteinThreshold>2</levenshteinThreshold>

                <!-- Optional: Custom category keywords -->
                <categoryKeywords>
                    <feature>
                        <param>feature</param>
                        <param>feat</param>
                        <param>new</param>
                        <param>add</param>
                        <param>implement</param>
                    </feature>
                    <bugfix>
                        <param>bugfix</param>
                        <param>fix</param>
                        <param>bug</param>
                        <param>hotfix</param>
                        <param>patch</param>
                    </bugfix>
                    <task>
                        <param>task</param>
                        <param>chore</param>
                        <param>refactor</param>
                        <param>cleanup</param>
                        <param>update</param>
                    </task>
                    <cid>
                        <param>cid</param>
                        <param>deployment</param>
                        <param>config</param>
                        <param>infrastructure</param>
                    </cid>
                </categoryKeywords>
            </configuration>
        </plugin>
    </plugins>
</build>
```

Then run with the shorter command:

```bash
mvn git-rellog:generate
```

### Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `repositoryDirectory` | File | `${project.basedir}` | Directory containing the Git repository |
| `includeSummary` | boolean | `true` | Whether to include summary statistics |
| `skip` | boolean | `false` | Whether to skip plugin execution |
| `maxTags` | int | `-1` | Maximum number of tags to process (-1 for no limit) |
| `outputDirectory` | File | `${project.basedir}` | Directory for output files |
| `saveToFiles` | boolean | `true` | Whether to save output to files |
| `categoryKeywords` | Map | See below | Custom categories and their keywords |
| `levenshteinThreshold` | int | `2` | Fuzzy matching threshold (0 to disable) |

### Command Line Properties

You can also override configuration using command line properties:

```bash
# Skip plugin execution
mvn git-rellog:generate -Dgit.rellog.skip=true

# Disable summary
mvn git-rellog:generate -Dgit.rellog.includeSummary=false

# Limit to 5 most recent tags
mvn git-rellog:generate -Dgit.rellog.maxTags=5

# Use different repository directory
mvn git-rellog:generate -Dgit.repository.dir=/path/to/repo

# Set custom Levenshtein threshold
mvn git-rellog:generate -Dgit.rellog.levenshteinThreshold=1

# Disable file output
mvn git-rellog:generate -Dgit.rellog.saveToFiles=false
```

### Default Categories

The plugin comes with these default categories and keywords:

- **feature**: feature, feat, new, add, implement
- **bugfix**: bugfix, fix, bug, hotfix, patch
- **task**: task, chore, refactor, cleanup, update
- **cid**: cid, deployment, config, infrastructure

### Branch Name Extraction

The plugin automatically extracts branch names from Bitbucket merge commit messages using patterns like:
- `Merged in feature/ABC-123/description (pull request #456)`
- Extracts: `feature/ABC-123/description`

### Commit Categorization Logic

1. **Exact keyword matching** in commit message and branch name
2. **Fuzzy matching** using Levenshtein distance (if enabled)
3. **Branch pattern analysis** (e.g., `feature/`, `bugfix/`, `task/`)
4. Falls back to **"uncategorized"** if no match found

### Output Files

When `saveToFiles` is enabled, the plugin generates:

- **`git-release-log.txt`**: Human-readable release log
- **`git-release-log.csv`**: CSV format for data analysis
- **`git-category-stats.txt`**: Category statistics and percentages
```

## Sample Output

```
================================================================================
GIT RELEASE LOG
================================================================================

📋 TAG: v5.0.36
📅 Date: 2025-07-28 13:02:52
🎯 Target Commit: d11b0d7
📊 Commits: 4
----------------------------------------
  🔸 d11b0d7 - Version 5.0.36 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-28 13:02:52

  🔸 1e6615a - Merged in fix/5.0.35/fix-mle-private-key (pull request #1302) [fix/5.0.35/fix-mle-private-key] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-28 13:01:43

  🔸 9faade9 - Merged in feature/CID/5.0.35 (pull request #1301) [feature/CID/5.0.35] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-28 10:04:48

  🔸 5213a72 - Version 5.0.36-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-25 19:02:50

📋 TAG: v5.0.35
📅 Date: 2025-07-25 18:57:23
🎯 Target Commit: 8a8a4db
📊 Commits: 6
----------------------------------------
  🔸 8a8a4db - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 18:57:23

  🔸 42bbcce - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 18:55:02

================================================================================
End of Git Release Log
================================================================================
📈 SUMMARY:
   Total Tags: 2
   Total Commits: 10
   Average Commits per Tag: 5.0

📊 CATEGORY STATISTICS:
   feature: 152 commits (31.0%)
   task: 151 commits (30.8%)
   bugfix: 141 commits (28.7%)
   cid: 47 commits (9.6%)
```

## Requirements

- Java 17 or higher
- Maven 3.5.4 or higher
- Git repository

## Building the Plugin

To build and install the plugin locally:

```bash
git clone <repository-url>
cd git-rellog-maven-plugin
mvn clean install
```

## License

This project is licensed under the MIT License.

## Author

**Nikolas Charalambidis**
- Email: <EMAIL>
- Website: https://www.nikolas-charalambidis.com
