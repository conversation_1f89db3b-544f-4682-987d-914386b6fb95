# Git Release Log Maven Plugin

A Maven plugin that generates a Git release log showing commits organized by tags. This plugin retrieves Git history from the current repository and displays commits grouped by their associated tags, providing a clear view of what changes were included in each release.

## Features

- 📋 **Tag-based Organization**: Groups commits by Git tags (releases)
- 🎯 **Commit Details**: Shows commit hash, message, author, and timestamp
- 📊 **Summary Statistics**: Displays total tags, commits, and averages
- 🔧 **Configurable**: Various options to customize output
- 🚀 **Easy Integration**: Simple Maven goal execution

## Usage

### Basic Usage

Run the plugin in any Git repository with Maven:

```bash
mvn com.nikolascharalambidis:git-rellog-maven-plugin:1.0-SNAPSHOT:generate
```

### Plugin Configuration

Add the plugin to your `pom.xml`:

```xml
<build>
    <plugins>
        <plugin>
            <groupId>com.nikolascharalambidis</groupId>
            <artifactId>git-rellog-maven-plugin</artifactId>
            <version>1.0-SNAPSHOT</version>
            <configuration>
                <!-- Optional: Specify repository directory (defaults to ${project.basedir}) -->
                <repositoryDirectory>${project.basedir}</repositoryDirectory>
                
                <!-- Optional: Include summary at the end (default: true) -->
                <includeSummary>true</includeSummary>
                
                <!-- Optional: Skip plugin execution (default: false) -->
                <skip>false</skip>
                
                <!-- Optional: Maximum number of tags to process (default: -1 for no limit) -->
                <maxTags>10</maxTags>
            </configuration>
        </plugin>
    </plugins>
</build>
```

Then run with the shorter command:

```bash
mvn git-rellog:generate
```

### Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `repositoryDirectory` | File | `${project.basedir}` | Directory containing the Git repository |
| `includeSummary` | boolean | `true` | Whether to include summary statistics |
| `skip` | boolean | `false` | Whether to skip plugin execution |
| `maxTags` | int | `-1` | Maximum number of tags to process (-1 for no limit) |

### Command Line Properties

You can also override configuration using command line properties:

```bash
# Skip plugin execution
mvn git-rellog:generate -Dgit.rellog.skip=true

# Disable summary
mvn git-rellog:generate -Dgit.rellog.includeSummary=false

# Limit to 5 most recent tags
mvn git-rellog:generate -Dgit.rellog.maxTags=5

# Use different repository directory
mvn git-rellog:generate -Dgit.repository.dir=/path/to/repo
```

## Sample Output

```
================================================================================
GIT RELEASE LOG
================================================================================

📋 TAG: v1.1.0
📅 Date: 2025-07-12 22:24:12
🎯 Target Commit: 017ef40
📊 Commits: 1
----------------------------------------
  🔸 017ef40 - Add documentation and testing
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-12 22:24:12

📋 TAG: v1.0.0
📅 Date: 2025-07-12 22:23:05
🎯 Target Commit: bf1b3a5
📊 Commits: 2
----------------------------------------
  🔸 bf1b3a5 - Initial release
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-12 22:23:05

  🔸 a1b2c3d - Fix critical bug
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-12 22:20:15

================================================================================
End of Git Release Log
================================================================================
📈 SUMMARY:
   Total Tags: 2
   Total Commits: 3
   Average Commits per Tag: 1.5
```

## Requirements

- Java 17 or higher
- Maven 3.5.4 or higher
- Git repository

## Building the Plugin

To build and install the plugin locally:

```bash
git clone <repository-url>
cd git-rellog-maven-plugin
mvn clean install
```

## License

This project is licensed under the MIT License.

## Author

**Nikolas Charalambidis**
- Email: <EMAIL>
- Website: https://www.nikolas-charalambidis.com
