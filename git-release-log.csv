Tag,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,Author,AuthorE<PERSON>,CommitDate,<PERSON><PERSON><PERSON>,Category
v5.0.36,2025-07-28 13:02:52,d11b0d7,Version 5.0.36,quickbuild,<EMAIL>,2025-07-28 13:02:52,,task
v5.0.36,2025-07-28 13:02:52,1e6615a,Merged in fix/5.0.35/fix-mle-private-key (pull request #1302),<PERSON><PERSON>,<EMAIL>,2025-07-28 13:01:43,fix/5.0.35/fix-mle-private-key,bugfix
v5.0.36,2025-07-28 13:02:52,9faade9,Merged in feature/CID/5.0.35 (pull request #1301),<PERSON><PERSON>,niko<PERSON>.charalambid<PERSON>@finshape.com,2025-07-28 10:04:48,feature/CID/5.0.35,feature
v5.0.36,2025-07-28 13:02:52,5213a72,Version 5.0.36-<PERSON><PERSON><PERSON><PERSON><PERSON>,quickbuild,<EMAIL>,2025-07-25 19:02:50,,task
v5.0.35,2025-07-25 18:57:23,8a8a4db,Unable to read commit message,Unknown,<EMAIL>,2025-07-25 18:57:23,,task
v5.0.35,2025-07-25 18:57:23,42bbcce,Unable to read commit message,Unknown,<EMAIL>,2025-07-25 18:55:02,,task
v5.0.35,2025-07-25 18:57:23,f56db08,Unable to read commit message,Unknown,<EMAIL>,2025-07-25 15:02:14,,task
v5.0.35,2025-07-25 18:57:23,46a89b4,Unable to read commit message,Unknown,<EMAIL>,2025-07-25 12:50:41,,task
v5.0.35,2025-07-25 18:57:23,3abd138,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 14:08:13,,task
v5.0.35,2025-07-25 18:57:23,806481d,Version 5.0.35-SNAPSHOT,quickbuild,<EMAIL>,2025-07-24 12:43:36,,task
v5.0.34,2025-07-24 12:37:51,2f8678c,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 12:37:51,,task
v5.0.34,2025-07-24 12:37:51,9b74497,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 11:39:39,,task
v5.0.34,2025-07-24 12:37:51,318a16a,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 11:02:55,,task
v5.0.34,2025-07-24 12:37:51,8766de8,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 11:02:55,,task
v5.0.34,2025-07-24 12:37:51,9797305,Unable to read commit message,Unknown,<EMAIL>,2025-07-24 09:52:01,,task
v5.0.34,2025-07-24 12:37:51,31d85a1,*********-435 - remove mapping for inapp visa google registration,Jakub Jirsa,<EMAIL>,2025-07-23 21:43:08,,task
v5.0.34,2025-07-24 12:37:51,a92bb8c,*********-7269 - remove formating dates depend on statement type for retail clients,Jakub Jirsa,<EMAIL>,2025-07-23 21:43:07,,task
v5.0.34,2025-07-24 12:37:51,5c0f2f9,NOJIRA: ExternalSystemIntegration documentation,Nikolas Charalambidis,<EMAIL>,2025-07-23 11:15:52,,task
v5.0.34,2025-07-24 12:37:51,c9bff7b,*********-417 - add option to get card provisioning data by card client and expiration date (only Google Mastercard),Jakub Jirsa,<EMAIL>,2025-07-22 20:08:43,,feature
v5.0.34,2025-07-24 12:37:51,a54bb68,*********-9084: Friends and Family - allow processing all clients,Nikolas Charalambidis,<EMAIL>,2025-07-22 13:57:56,,task
v5.0.34,2025-07-24 12:37:51,e127f39,fix build III,Milan Černil,<EMAIL>,2025-07-22 13:03:39,,bugfix
v5.0.34,2025-07-24 12:37:51,8dad8d9,fix build II,Milan Černil,<EMAIL>,2025-07-22 12:45:42,,bugfix
v5.0.34,2025-07-24 12:37:51,156d06f,cherrypick - Merged in feature/*********-9051/request/response-logging-log4j (pull request #1273) *********-9051 Review request/response logging (toString),Milan Černil,<EMAIL>,2025-07-22 12:33:07,feature/*********-9051/request/response-logging-log4j,feature
v5.0.34,2025-07-24 12:37:51,10ddb05,NOJIRA - build fix,Milan Černil,<EMAIL>,2025-07-22 12:30:27,,bugfix
v5.0.34,2025-07-24 12:37:51,32ef6f5,"*********-9075 - add generating rrn depend on source component, remap target account for credit IPS payment",Jakub Jirsa,<EMAIL>,2025-07-21 12:41:02,,feature
v5.0.34,2025-07-24 12:37:51,869cebe,Merged in feature/*********-50/upsThub (pull request #1287),Nikolas Charalambidis,<EMAIL>,2025-07-21 10:25:09,feature/*********-50/upsThub,feature
v5.0.34,2025-07-24 12:37:51,016d2ac,"CID 5.0.33 - INT2,DEV,UAT2",Milan Černil,<EMAIL>,2025-07-18 15:55:15,,cid
v5.0.34,2025-07-24 12:37:51,8f5ea5e,"CID 4.3.12 - INT,TEST,UAT",Milan Černil,<EMAIL>,2025-07-18 14:44:38,,cid
v5.0.34,2025-07-24 12:37:51,3560f0e,Version 5.0.34-SNAPSHOT,quickbuild,<EMAIL>,2025-07-18 14:19:15,,task
v5.0.34,2025-07-24 12:37:51,808b5fd,Version 5.0.33,quickbuild,<EMAIL>,2025-07-18 14:13:44,,task
v5.0.34,2025-07-24 12:37:51,7fc4383,Merged in task/NOJIRA/CID.md (pull request #1280),Nikolas Charalambidis,<EMAIL>,2025-07-18 13:50:17,task/NOJIRA/CID.md,task
v5.0.34,2025-07-24 12:37:51,283a094,Merged in fix/*********-7269/use-create-at (pull request #1284),Jakub Jirsa,<EMAIL>,2025-07-18 13:37:43,fix/*********-7269/use-create-at,bugfix
v5.0.34,2025-07-24 12:37:51,a37df46,Merged in feature/4.3/*********-8982/Add-PIN-for-new-card (pull request #1286),Milan Černil,<EMAIL>,2025-07-18 13:29:14,feature/4.3/*********-8982/Add-PIN-for-new-card,feature
v5.0.34,2025-07-24 12:37:51,d552e76,Merged in feat/cms-ln-pwd-support-variable (pull request #1285),Tomáš Lipovský,<EMAIL>,2025-07-18 11:30:11,feat/cms-ln-pwd-support-variable,feature
v5.0.34,2025-07-24 12:37:51,0b9a264,Merged in bugfix/*********-9027/gdeExceptionWithRouteOutMessageData (pull request #1278),Nikolas Charalambidis,<EMAIL>,2025-07-18 09:41:49,bugfix/*********-9027/gdeExceptionWithRouteOutMessageData,bugfix
v5.0.34,2025-07-24 12:37:51,817533d,Merged in bugfix/*********-8982/improvement (pull request #1258),Nikolas Charalambidis,<EMAIL>,2025-07-18 09:41:39,bugfix/*********-8982/improvement,bugfix
v5.0.34,2025-07-24 12:37:51,dee117f,Merged in fix/ln-pwd-prod (pull request #1281),Tomáš Lipovský,<EMAIL>,2025-07-17 16:48:57,fix/ln-pwd-prod,bugfix
v5.0.34,2025-07-24 12:37:51,e9e3b32,Merged in feat/cms-redirect (pull request #1276),Tomáš Lipovský,<EMAIL>,2025-07-17 13:12:49,feat/cms-redirect,feature
v5.0.34,2025-07-24 12:37:51,b7caade,Merged in feature/*********-50/head (pull request #1279),Nikolas Charalambidis,<EMAIL>,2025-07-17 09:05:06,feature/*********-50/head,feature
v5.0.34,2025-07-24 12:37:51,4dd76f3,*********-50: Subscription management - TPM-GDE-UPS,Jakub Jirsa,<EMAIL>,2025-07-15 21:31:07,,task
v5.0.34,2025-07-24 12:37:51,1964f4c,Merged in fix/4.3/*********-8859/null-sum (pull request #1277),Milan Černil,<EMAIL>,2025-07-15 16:09:35,fix/4.3/*********-8859/null-sum,bugfix
v5.0.34,2025-07-24 12:37:51,83ee9e7,Merged in fix/redirect-uri-2 (pull request #1274),Tomáš Lipovský,<EMAIL>,2025-07-15 14:39:15,fix/redirect-uri-2,bugfix
v5.0.34,2025-07-24 12:37:51,38cabed,CID: CMS update env variables,Jan Dockal,<EMAIL>,2025-07-15 13:52:52,,task
v5.0.34,2025-07-24 12:37:51,d1e6999,Document upload fix + 3D secure app link fix,Milan Černil,<EMAIL>,2025-07-15 11:08:33,,bugfix
v5.0.34,2025-07-24 12:37:51,0b4669f,*********-7269 - fix statement date format,Jakub Jirsa,<EMAIL>,2025-07-15 10:06:55,,bugfix
v5.0.34,2025-07-24 12:37:51,14fd9a9,Merged in feature/*********-50/e2eFix (pull request #1268),Nikolas Charalambidis,<EMAIL>,2025-07-14 13:41:14,feature/*********-50/e2eFix,feature
v5.0.34,2025-07-24 12:37:51,6ae4f5b,Merged in bugfix/*********-8640/gaas16.1.0-M.6 (pull request #1272),Nikolas Charalambidis,<EMAIL>,2025-07-14 13:21:12,bugfix/*********-8640/gaas16.1.0-M.6,bugfix
v5.0.34,2025-07-24 12:37:51,f4b3b19,Merged in bugfix/*********-910/improvement (pull request #1271),Nikolas Charalambidis,<EMAIL>,2025-07-14 09:44:13,bugfix/*********-910/improvement,bugfix
v5.0.34,2025-07-24 12:37:51,77d7255,NOJIRA: Fixes e2e-test.properties,Nikolas Charalambidis,<EMAIL>,2025-07-10 12:12:25,,bugfix
v5.0.34,2025-07-24 12:37:51,a116dfb,Version 5.0.33-SNAPSHOT,quickbuild,<EMAIL>,2025-07-10 12:11:20,,task
v5.0.34,2025-07-24 12:37:51,0eb5a9c,Version 5.0.32,quickbuild,<EMAIL>,2025-07-10 12:05:44,,task
v5.0.34,2025-07-24 12:37:51,6c50198,Merged in task/*********-915/5.0.31-fixes (pull request #1267),Nikolas Charalambidis,<EMAIL>,2025-07-10 11:59:05,task/*********-915/5.0.31-fixes,task
v5.0.34,2025-07-24 12:37:51,e716e40,"*********-7832: fix loading last updated date from statement API, fix typo in template, fix stretch for remittance information",Jakub Jirsa,<EMAIL>,2025-07-10 09:48:38,,task
v5.0.34,2025-07-24 12:37:51,9c2d215,Merged in task/*********-915/5.0.31-fixes (pull request #1263),Nikolas Charalambidis,<EMAIL>,2025-07-10 09:06:25,task/*********-915/5.0.31-fixes,task
v5.0.34,2025-07-24 12:37:51,3d72eab,Merged in feature/*********-778-Component-UPS-CID (pull request #1264),Nikolas Charalambidis,<EMAIL>,2025-07-09 21:49:27,feature/*********-778-Component-UPS-CID,feature
v5.0.34,2025-07-24 12:37:51,c459cf5,Merged in feature/*********-50/ups-payment-table (pull request #1261),Nikolas Charalambidis,<EMAIL>,2025-07-08 11:47:55,feature/*********-50/ups-payment-table,feature
v5.0.34,2025-07-24 12:37:51,5372c49,Version 5.0.32-SNAPSHOT,quickbuild,<EMAIL>,2025-07-08 11:24:25,,task
v5.0.34,2025-07-24 12:37:51,99283d9,Version 5.0.31,quickbuild,<EMAIL>,2025-07-08 11:18:54,,task
v5.0.34,2025-07-24 12:37:51,adcfaad,Merged in feature/*********-50/ups (pull request #1204),Jakub Jirsa,<EMAIL>,2025-07-08 11:08:26,feature/*********-50/ups,feature
v5.0.34,2025-07-24 12:37:51,fd45526,*********-7068 - fix floating frame position in retail template,Jakub Jirsa,<EMAIL>,2025-07-04 11:42:40,,bugfix
v5.0.34,2025-07-24 12:37:51,8a0ccce,Fix/*********-7068/fix retail template,Jakub Jirsa,<EMAIL>,2025-07-04 11:42:38,,bugfix
v5.0.34,2025-07-24 12:37:51,81b7e08,*********-8354 - fix print to TXT and XML,Jakub Jirsa,<EMAIL>,2025-07-04 11:42:35,,bugfix
v5.0.34,2025-07-24 12:37:51,df94a7f,Merge remote-tracking branch 'origin/develop' into develop,Jakub Jirsa,<EMAIL>,2025-07-04 11:42:15,,task
v5.0.34,2025-07-24 12:37:51,c3025cc,"*********-8830 - change pdf creator to ""B.C. VICTORIABANK S.A."" and producer to ""Finshape Global Print Server"", merge with develop",Jakub Jirsa,<EMAIL>,2025-07-04 11:41:51,,task
v5.0.34,2025-07-24 12:37:51,ffb2f44,"*********-8830 - change pdf creator to ""B.C. VICTORIABANK S.A."" and producer to ""Finshape Global Print Server""",Jakub Jirsa,<EMAIL>,2025-07-04 11:37:07,,task
v5.0.34,2025-07-24 12:37:51,a13dcfb,Merged in fix/4.3/*********-8979/29-Lack-of-User-Input-Validation-Length (pull request #1255),Milan Černil,<EMAIL>,2025-07-04 10:50:26,fix/4.3/*********-8979/29-Lack-of-User-Input-Validation-Length,bugfix
v5.0.34,2025-07-24 12:37:51,95e542d,Version 5.0.31-SNAPSHOT,quickbuild,<EMAIL>,2025-07-03 16:31:45,,task
v5.0.34,2025-07-24 12:37:51,e02b0fe,Version 5.0.30,quickbuild,<EMAIL>,2025-07-03 16:26:16,,task
v5.0.34,2025-07-24 12:37:51,382121a,fix test,Milan Černil,<EMAIL>,2025-07-03 16:17:34,,bugfix
v5.0.34,2025-07-24 12:37:51,f443832,Build fix,Milan Černil,<EMAIL>,2025-07-03 14:58:20,,bugfix
v5.0.34,2025-07-24 12:37:51,23af2e1,CID: CMS bump version,Jan Dockal,<EMAIL>,2025-07-03 14:20:07,,cid
v5.0.34,2025-07-24 12:37:51,a26c9ff,Merged in feature/4.3/*********-8859/SPX/documents-upload-throttling (pull request #1249) *********-8859 [Documents] BE SPX - Define and Implement File Upload Limits to Prevent System Overload,Milan Černil,<EMAIL>,2025-07-03 13:00:16,feature/4.3/*********-8859/SPX/documents-upload-throttling,feature
v5.0.34,2025-07-24 12:37:51,fcb8a5c,CID: WEB change versioning of monorepo,Jan Dockal,<EMAIL>,2025-07-03 10:20:50,,cid
v5.0.34,2025-07-24 12:37:51,5f0b251,CID: 5.0.29 (VB INT2),Nikolas Charalambidis,<EMAIL>,2025-07-02 18:48:20,,cid
v5.0.34,2025-07-24 12:37:51,b01f487,"CID: 4.3.7 (Finshape TEST, VB INT)",Nikolas Charalambidis,<EMAIL>,2025-07-02 17:47:43,,cid
v5.0.34,2025-07-24 12:37:51,504f16f,Version 5.0.30-SNAPSHOT,quickbuild,<EMAIL>,2025-07-02 17:10:11,,task
v5.0.34,2025-07-24 12:37:51,f6e1472,Version 5.0.29,quickbuild,<EMAIL>,2025-07-02 17:04:42,,task
v5.0.34,2025-07-24 12:37:51,ca244a7,Merged in bugfix/*********-677/aliasProfileNameFix (pull request #1250),Nikolas Charalambidis,<EMAIL>,2025-07-02 16:25:35,bugfix/*********-677/aliasProfileNameFix,bugfix
v5.0.34,2025-07-24 12:37:51,cdf5ea3,Merged in feature/*********-697/swiftCatalogsDeadLettering2 (pull request #1246),Nikolas Charalambidis,<EMAIL>,2025-07-02 15:14:01,feature/*********-697/swiftCatalogsDeadLettering2,feature
v5.0.34,2025-07-24 12:37:51,b9f9492,Merged in bugfix/nojira/p2pPaymentOrderE2ETestFix (pull request #1247),Nikolas Charalambidis,<EMAIL>,2025-07-02 15:13:54,bugfix/nojira/p2pPaymentOrderE2ETestFix,bugfix
v5.0.34,2025-07-24 12:37:51,452ecdb,Merged in feature/*********-8858/maxAttachmentCountValidator (pull request #1245),Nikolas Charalambidis,<EMAIL>,2025-07-02 12:34:09,feature/*********-8858/maxAttachmentCountValidator,feature
v5.0.34,2025-07-24 12:37:51,a082dd9,CID: WAC bump version,Jan Dockal,<EMAIL>,2025-07-01 15:53:42,,cid
v5.0.34,2025-07-24 12:37:51,083229a,Merged in feature/*********-8694/gaapiDoSProtection2 (pull request #1244),Nikolas Charalambidis,<EMAIL>,2025-07-01 12:11:23,feature/*********-8694/gaapiDoSProtection2,feature
v5.0.34,2025-07-24 12:37:51,66184c5,Merged in fix/max-sessions-develop (pull request #1218),Tomáš Lipovský,<EMAIL>,2025-07-01 11:13:04,fix/max-sessions-develop,bugfix
v5.0.34,2025-07-24 12:37:51,76758e5,*********-8614: More Friends & Family,Nikolas Charalambidis,<EMAIL>,2025-06-30 14:35:37,,task
v5.0.34,2025-07-24 12:37:51,b407329,"Merged in fix/4.3/*********-492/Movements-new-gateway (pull request #1237) *********-492 new gateway address, cid adjusted",Milan Černil,<EMAIL>,2025-06-30 07:55:05,fix/4.3/*********-492/Movements-new-gateway,feature
v5.0.34,2025-07-24 12:37:51,95b6fef,Merged in fix/NOJIRA/k8s-config (pull request #1238),Jan Holec,<EMAIL>,2025-06-27 22:02:20,fix/NOJIRA/k8s-config,bugfix
v5.0.34,2025-07-24 12:37:51,ae42d1c,CID: 5.0.28 (VB INT2),Nikolas Charalambidis,<EMAIL>,2025-06-27 15:14:47,,cid
v5.0.34,2025-07-24 12:37:51,5b02ec4,Version 5.0.29-SNAPSHOT,quickbuild,<EMAIL>,2025-06-27 14:02:46,,task
v5.0.34,2025-07-24 12:37:51,1b850c9,Version 5.0.28,quickbuild,<EMAIL>,2025-06-27 13:57:12,,task
v5.0.34,2025-07-24 12:37:51,880cf25,"CID: 4.3.6 (Finshape TEST, VB INT)",Nikolas Charalambidis,<EMAIL>,2025-06-27 13:52:05,,cid
v5.0.34,2025-07-24 12:37:51,446e14a,Merged in bugfix/*********-555/rrnMappingForP2PFix (pull request #1232),Nikolas Charalambidis,<EMAIL>,2025-06-27 13:48:45,bugfix/*********-555/rrnMappingForP2PFix,bugfix
v5.0.34,2025-07-24 12:37:51,08256a3,*********-7269 - anonymize data for IPS prints,Jakub Jirsa,<EMAIL>,2025-06-27 11:16:50,,task
v5.0.34,2025-07-24 12:37:51,ab3a868,CID: CMS upgrade migration scripts,Jan Dockal,<EMAIL>,2025-06-27 10:39:53,,cid
v5.0.34,2025-07-24 12:37:51,7033374,Merged in bugfix/4.3/*********-8959 (pull request #1234),Nikolas Charalambidis,<EMAIL>,2025-06-27 10:32:16,bugfix/4.3/*********-8959,bugfix
v5.0.34,2025-07-24 12:37:51,4e3983f,Remove E2E Test annotation,Milan Černil,<EMAIL>,2025-06-27 10:14:35,,task
v5.0.34,2025-07-24 12:37:51,799a66d,Merged in feature/1b/*********-8893/pin-and-e2e (pull request #1236),Milan Černil,<EMAIL>,2025-06-27 10:01:31,feature/1b/*********-8893/pin-and-e2e,feature
v5.0.34,2025-07-24 12:37:51,551f876,*********-8916 Add Activate card to Digital Retail Card Creation - e2e test,Milan Černil,<EMAIL>,2025-06-26 15:26:52,,feature
v5.0.34,2025-07-24 12:37:51,29b6601,Merged in feature/4.3/*********-8916/Add-Activate-card-to-Digital-Retail-Card-Creation (pull request #1231),Milan Černil,<EMAIL>,2025-06-26 10:57:40,feature/4.3/*********-8916/Add-Activate-card-to-Digital-Retail-Card-Creation,feature
v5.0.34,2025-07-24 12:37:51,e6f3a91,CID: CMS upgrade migration scripts,Jan Dockal,<EMAIL>,2025-06-26 09:33:38,,cid
v5.0.34,2025-07-24 12:37:51,be7d6c7,Merged in feature/*********-691/swiftCatalogMappings (pull request #1203),Nikolas Charalambidis,<EMAIL>,2025-06-24 11:26:46,feature/*********-691/swiftCatalogMappings,feature
v5.0.34,2025-07-24 12:37:51,f7ef4b7,Merged in bugfix/*********-8932/displayIdFix (pull request #1229),Nikolas Charalambidis,<EMAIL>,2025-06-24 11:24:59,bugfix/*********-8932/displayIdFix,bugfix
v5.0.34,2025-07-24 12:37:51,897b229,*********-23 - bruno collection for inapp (hswm wallet),Jakub Jirsa,<EMAIL>,2025-06-24 10:14:02,,task
v5.0.34,2025-07-24 12:37:51,0af02cb,Merged in bugfix/*********-8918/cardGeneratorFix (pull request #1225),Nikolas Charalambidis,<EMAIL>,2025-06-23 15:40:45,bugfix/*********-8918/cardGeneratorFix,bugfix
v5.0.34,2025-07-24 12:37:51,883bef0,Merged in feature/*********-8337/friendsAndFamily6 (pull request #1220),Nikolas Charalambidis,<EMAIL>,2025-06-23 15:40:16,feature/*********-8337/friendsAndFamily6,feature
v5.0.34,2025-07-24 12:37:51,d15be7a,Merged in feature/*********-697/swiftCatalogsDeadLettering (pull request #1202),Nikolas Charalambidis,<EMAIL>,2025-06-23 09:22:25,feature/*********-697/swiftCatalogsDeadLettering,feature
v5.0.34,2025-07-24 12:37:51,bed02ca,Merged in feat/deploy-prod-yaml (pull request #1215),Tomáš Lipovský,<EMAIL>,2025-06-20 16:05:05,feat/deploy-prod-yaml,feature
v5.0.34,2025-07-24 12:37:51,e7491ea,set variable values for session parameters,Tomas Lipovsky,<EMAIL>,2025-06-20 16:05:05,,task
v5.0.34,2025-07-24 12:37:51,257cc05,E2E - fix Treasury,Milan Černil,<EMAIL>,2025-06-20 11:22:15,,bugfix
v5.0.34,2025-07-24 12:37:51,8fe8fb5,"*********-8888 - fix changing RTP status to accepted after pay, add scheduled job which delete RTPs older than 30d",Jakub Jirsa,<EMAIL>,2025-06-20 10:31:19,,feature
v5.0.34,2025-07-24 12:37:51,7bfac41,Merged in feature/*********-8337/friendsAndFamily5 (pull request #1212),Nikolas Charalambidis,<EMAIL>,2025-06-20 10:13:58,feature/*********-8337/friendsAndFamily5,feature
v5.0.34,2025-07-24 12:37:51,61cced3,Merged in fix/E2E (pull request #1214),Milan Černil,<EMAIL>,2025-06-19 16:20:30,fix/E2E,bugfix
v5.0.34,2025-07-24 12:37:51,2bb0d88,Merged in fix/4.3/*********-7675/Liquibase-master-changelog-issues (pull request #1211),Milan Černil,<EMAIL>,2025-06-19 09:58:16,fix/4.3/*********-7675/Liquibase-master-changelog-issues,bugfix
v5.0.34,2025-07-24 12:37:51,4ad1f73,E2E tests fix II,Milan Černil,<EMAIL>,2025-06-19 08:03:43,,bugfix
v5.0.34,2025-07-24 12:37:51,c53e905,E2E tests fix,Milan Černil,<EMAIL>,2025-06-19 07:42:34,,bugfix
v5.0.34,2025-07-24 12:37:51,e8dc096,CID: 4.3.2 (Finshape TEST),Nikolas Charalambidis,<EMAIL>,2025-06-18 15:16:36,,cid
v5.0.34,2025-07-24 12:37:51,cd9be55,Merged in feature/4.3/movementRrnStored (pull request #1208),Milan Černil,<EMAIL>,2025-06-18 13:41:34,feature/4.3/movementRrnStored,feature
v5.0.34,2025-07-24 12:37:51,5dd7cd2,Merged in feature/*********-8874/friendsAndFamilyDummyMode (pull request #1205),Nikolas Charalambidis,<EMAIL>,2025-06-18 13:40:52,feature/*********-8874/friendsAndFamilyDummyMode,feature
v5.0.34,2025-07-24 12:37:51,42bb2b3,Merged in feature/*********-8337/friendsAndFamily4 (pull request #1206),Nikolas Charalambidis,<EMAIL>,2025-06-18 13:24:14,feature/*********-8337/friendsAndFamily4,feature
v5.0.34,2025-07-24 12:37:51,1c9e27b,Merged in bugfix/*********-8873/xcrLimitsReplicationFix (pull request #1207),Nikolas Charalambidis,<EMAIL>,2025-06-18 13:24:14,bugfix/*********-8873/xcrLimitsReplicationFix,bugfix
v5.0.34,2025-07-24 12:37:51,b199c31,Merged in bugfix/*********-7716/instructionStatusTransitionListenerImprovements (pull request #1201),Nikolas Charalambidis,<EMAIL>,2025-06-18 09:19:42,bugfix/*********-7716/instructionStatusTransitionListenerImprovements,bugfix
v5.0.34,2025-07-24 12:37:51,782cd6c,Merged in feature/gaas-update-cherry (pull request #1199),Milan Černil,<EMAIL>,2025-06-18 07:47:43,feature/gaas-update-cherry,task
v5.0.34,2025-07-24 12:37:51,bbc1026,Merged in fix/4.2/deployer-shell-fixes (pull request #1183),Milan Černil,<EMAIL>,2025-06-17 08:12:49,fix/4.2/deployer-shell-fixes,bugfix
v5.0.34,2025-07-24 12:37:51,261b47b,CID: 4.3.1 (Finshape TEST),Nikolas Charalambidis,<EMAIL>,2025-06-16 10:32:31,,cid
v5.0.34,2025-07-24 12:37:51,17259af,"*********-8830 - change pdf creator to ""B.C. VICTORIABANK S.A."" from Jasper 6.21.2",Jakub Jirsa,<EMAIL>,2025-06-16 09:58:15,,task
v5.0.34,2025-07-24 12:37:51,2ba1c71,Merged in feature/*********-681/swiftCatalogUpdate (pull request #1198),Nikolas Charalambidis,<EMAIL>,2025-06-16 09:51:12,feature/*********-681/swiftCatalogUpdate,task
v5.0.34,2025-07-24 12:37:51,fe7fe10,Merged in feature/*********-8337/friendsAndFamily3 (pull request #1200),Nikolas Charalambidis,<EMAIL>,2025-06-16 09:30:30,feature/*********-8337/friendsAndFamily3,feature
v5.0.34,2025-07-24 12:37:51,7f19539,CID: 4.2.25 (VB INT),Milan Černil,<EMAIL>,2025-06-12 09:12:44,,cid
v5.0.34,2025-07-24 12:37:51,e2dec5b,"CID INT, INT2, UAT, UAT2 GAAS dummy user *************",Milan Černil,<EMAIL>,2025-06-12 09:11:31,,cid
v5.0.34,2025-07-24 12:37:51,ae8bf71,Merged in feature/*********-8337/friendsAndFamily2 (pull request #1196),Nikolas Charalambidis,<EMAIL>,2025-06-12 08:51:57,feature/*********-8337/friendsAndFamily2,feature
v5.0.34,2025-07-24 12:37:51,05d0f12,*********-8801 - add enriching residency into IPS*instruction,Jakub Jirsa,<EMAIL>,2025-06-11 21:43:28,,feature
v5.0.34,2025-07-24 12:37:51,55f5f75,"CID: 4.2.25 (Finshape TEST, VB UAT)",Nikolas Charalambidis,<EMAIL>,2025-06-11 18:15:58,,cid
v5.0.34,2025-07-24 12:37:51,52c1744,Merged in feature/*********-8628/redirectUrlFix (pull request #1195),Nikolas Charalambidis,<EMAIL>,2025-06-11 18:14:32,feature/*********-8628/redirectUrlFix,feature
v5.0.34,2025-07-24 12:37:51,3f91a45,Merged in bugfix/*********-8791/npeFixInValidators (pull request #1192),Nikolas Charalambidis,<EMAIL>,2025-06-11 15:05:25,bugfix/*********-8791/npeFixInValidators,bugfix
v5.0.34,2025-07-24 12:37:51,dd03ce8,*********-8795 - fix loading data from GDS for POS report,Jakub Jirsa,<EMAIL>,2025-06-11 14:12:29,,bugfix
v5.0.34,2025-07-24 12:37:51,a3efc16,fixup! *********-8153 - rename property virtual-host to virtualHost,Jan Dockal,<EMAIL>,2025-06-11 13:05:18,,bugfix
v5.0.34,2025-07-24 12:37:51,c4d5cad,*********-8153 - rename property virtual-host to virtualHost,Jakub Jirsa,<EMAIL>,2025-06-11 12:16:45,,task
v5.0.34,2025-07-24 12:37:51,10749fc,"*********-7814 - fix locale properties for prints, add double spaces for statement list, adjust bru collection",Jakub Jirsa,<EMAIL>,2025-06-11 12:09:10,,feature
v5.0.34,2025-07-24 12:37:51,61832b7,CID: CMS - bump migrations version,Jan Dockal,<EMAIL>,2025-06-11 12:06:09,,cid
v5.0.34,2025-07-24 12:37:51,2d081e9,CID: CMS - bump migrations version,Jan Dockal,<EMAIL>,2025-06-11 11:54:16,,cid
v5.0.34,2025-07-24 12:37:51,05feb6a,*********-8694: GAAPI protection against DDoS using rate limiter,Nikolas Charalambidis,<EMAIL>,2025-06-11 11:13:52,,task
v5.0.34,2025-07-24 12:37:51,c51406e,Merged in feature/*********-15/resolveAliasProfileNullHandling (pull request #1188),Nikolas Charalambidis,<EMAIL>,2025-06-11 09:42:29,feature/*********-15/resolveAliasProfileNullHandling,feature
v5.0.34,2025-07-24 12:37:51,0f72b60,Merged in fix/env (pull request #1181),Tomáš Lipovský,<EMAIL>,2025-06-10 11:34:30,fix/env,bugfix
v5.0.34,2025-07-24 12:37:51,4717b25,Merged in fix/4.2/*********-8793/spx-file-name-sanitation (pull request #1184),Milan Černil,<EMAIL>,2025-06-10 09:55:58,fix/4.2/*********-8793/spx-file-name-sanitation,bugfix
v5.0.34,2025-07-24 12:37:51,69f1994,Merged in feature/*********-370/componentUpdates (pull request #1008),Nikolas Charalambidis,<EMAIL>,2025-06-09 10:25:03,feature/*********-370/componentUpdates,task
v5.0.34,2025-07-24 12:37:51,46ec1ad,*********-8053 - NoTomorrowValidator - attribute validator for input date,Jakub Jirsa,<EMAIL>,2025-06-09 10:13:13,,task
v5.0.34,2025-07-24 12:37:51,b719a58,Merged in feature/*********-8337/friendsAndFamilyFixes (pull request #1179),Nikolas Charalambidis,<EMAIL>,2025-06-09 09:52:35,feature/*********-8337/friendsAndFamilyFixes,feature
v5.0.34,2025-07-24 12:37:51,9615a69,Merged in bugfix/*********-8785/mdFiscalCodeValidator (pull request #1178),Nikolas Charalambidis,<EMAIL>,2025-06-09 09:35:50,bugfix/*********-8785/mdFiscalCodeValidator,bugfix
v5.0.34,2025-07-24 12:37:51,419ee88,Merged in feature/NOJIRA/configDeployerWatchFilePattern (pull request #1182),Nikolas Charalambidis,<EMAIL>,2025-06-09 09:07:52,feature/NOJIRA/configDeployerWatchFilePattern,feature
v5.0.34,2025-07-24 12:37:51,0b49b88,Merged in feat/*********-8687-gaas-env-var (pull request #1164),Tomáš Lipovský,<EMAIL>,2025-06-06 16:56:01,feat/*********-8687-gaas-env-var,feature
v5.0.34,2025-07-24 12:37:51,817764a,"*********-7632 - fix dateFrom and dateTo for POS statements report, add timezoneOffsetInMinutes into print req",Jakub Jirsa,<EMAIL>,2025-06-06 15:45:46,,feature
v5.0.34,2025-07-24 12:37:51,8fc306d,*********-8781 - fix cid for GPS,Jakub Jirsa,<EMAIL>,2025-06-06 15:45:45,,bugfix
v5.0.34,2025-07-24 12:37:51,9be938a,Merged in feature/*********-8337/friendsAndFamilyAdjustments (pull request #1176),Nikolas Charalambidis,<EMAIL>,2025-06-06 15:38:02,feature/*********-8337/friendsAndFamilyAdjustments,feature
v5.0.34,2025-07-24 12:37:51,e80d04e,Merged in feature/*********-8337/friendsAndFamilyDevelop (pull request #1174),Nikolas Charalambidis,<EMAIL>,2025-06-06 13:16:18,feature/*********-8337/friendsAndFamilyDevelop,feature
v5.0.34,2025-07-24 12:37:51,64e1622,"CID: 4.2.22 (VB INT, VB UAT) + syntax fixes",Nikolas Charalambidis,<EMAIL>,2025-06-06 11:33:41,,bugfix
v5.0.34,2025-07-24 12:37:51,c3c571b,CID: ib-web set GPX_PUBLIC_URL,Jan Dockal,<EMAIL>,2025-06-06 08:44:37,,cid
v5.0.34,2025-07-24 12:37:51,54067ed,CID: 4.2.22 (Finshape TEST) + merge fixes,Nikolas Charalambidis,<EMAIL>,2025-06-05 15:10:48,,bugfix
v5.0.34,2025-07-24 12:37:51,2340a09,NOJIRA: Merge fixes,Nikolas Charalambidis,<EMAIL>,2025-06-05 13:39:17,,bugfix
v5.0.34,2025-07-24 12:37:51,ea5f9f8,Merged in feature/*********-8765/addPhysicalCardToDigitalProductEligibilityValidatorRenamed (pull request #1173),Nikolas Charalambidis,<EMAIL>,2025-06-05 13:30:02,feature/*********-8765/addPhysicalCardToDigitalProductEligibilityValidatorRenamed,feature
v5.0.34,2025-07-24 12:37:51,7d773a6,Merged in bugfix/*********-7434/documentNumberAsString (pull request #1172),Nikolas Charalambidis,<EMAIL>,2025-06-05 13:29:22,bugfix/*********-7434/documentNumberAsString,bugfix
v5.0.34,2025-07-24 12:37:51,475caaa,Merged in feature/*********-8337/friendsAndFamily (pull request #1118),Nikolas Charalambidis,<EMAIL>,2025-06-05 13:29:16,feature/*********-8337/friendsAndFamily,feature
v5.0.34,2025-07-24 12:37:51,0225d03,NOJIRA: Compilation fix,Nikolas Charalambidis,<EMAIL>,2025-06-05 13:29:00,,bugfix
v5.0.34,2025-07-24 12:37:51,08ab91c,*********-8686 - Sanitize input string values to prevent Excel formula injection,Jakub Jirsa,<EMAIL>,2025-06-05 11:24:45,,task
v5.0.34,2025-07-24 12:37:51,32f6a50,*********-7069 / remove redundant fileds,Jakub Jirsa,<EMAIL>,2025-06-05 11:24:44,,task
v5.0.34,2025-07-24 12:37:51,7061e42,*********-23 - rework inapp for Google,Jakub Jirsa,<EMAIL>,2025-06-05 11:24:43,,task
v5.0.34,2025-07-24 12:37:51,705231b,"*********-8667/upgrade gps sider, fix CVE-2025-48734 and CVE-2025-31672 for GPS",Jakub Jirsa,<EMAIL>,2025-06-05 11:23:40,,bugfix
v5.0.34,2025-07-24 12:37:51,c8da546,Version 5.0.28-SNAPSHOT,quickbuild,<EMAIL>,2025-06-04 19:11:46,,task
v5.0.34,2025-07-24 12:37:51,193b34a,Version 5.0.27,quickbuild,<EMAIL>,2025-06-04 19:06:11,,task
v5.0.34,2025-07-24 12:37:51,13d3bae,Merged in feature/*********-638/fix (pull request #1169),Nikolas Charalambidis,<EMAIL>,2025-06-04 18:57:41,feature/*********-638/fix,feature
v5.0.34,2025-07-24 12:37:51,385b575,"CID: 5.0.26 (Finshape DEV, VB INT2, VB UAT2)",Nikolas Charalambidis,<EMAIL>,2025-06-04 14:26:49,,cid
v5.0.34,2025-07-24 12:37:51,6816ade,Version 5.0.27-SNAPSHOT,quickbuild,<EMAIL>,2025-06-04 13:51:22,,task
v5.0.34,2025-07-24 12:37:51,a43dad6,Version 5.0.26,quickbuild,<EMAIL>,2025-06-04 13:45:23,,task
v5.0.34,2025-07-24 12:37:51,f0d0273,Merged in feature/*********-8676/e2eAdjustmentsForNewMandatoryFields (pull request #1167),Nikolas Charalambidis,<EMAIL>,2025-06-04 11:27:19,feature/*********-8676/e2eAdjustmentsForNewMandatoryFields,feature
v5.0.34,2025-07-24 12:37:51,31902ed,Merged in feature/*********-15/aliasInquiryBatching (pull request #1165),Nikolas Charalambidis,<EMAIL>,2025-06-04 09:32:52,feature/*********-15/aliasInquiryBatching,feature
v5.0.34,2025-07-24 12:37:51,b61b56d,Merged in feature/*********-638/adjustAddCardP2PAliasRequestProcessing-Flow (pull request #1163),Nikolas Charalambidis,<EMAIL>,2025-06-03 20:09:20,feature/*********-638/adjustAddCardP2PAliasRequestProcessing-Flow,feature
v5.0.34,2025-07-24 12:37:51,153d787,Merged in feature/*********-8676/maskNumberEnriching (pull request #1161),Nikolas Charalambidis,<EMAIL>,2025-06-03 09:32:50,feature/*********-8676/maskNumberEnriching,feature
v5.0.34,2025-07-24 12:37:51,79596fd,Version 5.0.26-SNAPSHOT,quickbuild,<EMAIL>,2025-06-02 18:46:24,,task
v5.0.34,2025-07-24 12:37:51,b1820c1,Version 5.0.25,quickbuild,<EMAIL>,2025-06-02 18:40:48,,task
v5.0.34,2025-07-24 12:37:51,8281208,Merged in task/*********-8609/dryRunCherryPick (pull request #1160),Nikolas Charalambidis,<EMAIL>,2025-06-02 18:15:45,task/*********-8609/dryRunCherryPick,task
v5.0.34,2025-07-24 12:37:51,748c3ab,Merged in task/*********-632/branchIdEnrichingFix (pull request #1159),Nikolas Charalambidis,<EMAIL>,2025-06-02 17:32:03,task/*********-632/branchIdEnrichingFix,task
v5.0.34,2025-07-24 12:37:51,b38f9b2,Version 5.0.25-SNAPSHOT,quickbuild,<EMAIL>,2025-06-01 21:34:42,,task
v5.0.34,2025-07-24 12:37:51,bf5f7d3,Version 5.0.24,quickbuild,<EMAIL>,2025-06-01 21:28:31,,task
v5.0.34,2025-07-24 12:37:51,4614ff9,Merged in fix/4.2/*********-7517-VB/Treasury-fields (pull request #1157),Milan Černil,<EMAIL>,2025-06-01 21:14:17,fix/4.2/*********-7517-VB/Treasury-fields,bugfix
v5.0.34,2025-07-24 12:37:51,f260df5,Merged in fix/4.2/*********-8613/Treasury-Remittance-overriden (pull request #1156),Milan Černil,<EMAIL>,2025-06-01 17:48:11,fix/4.2/*********-8613/Treasury-Remittance-overriden,bugfix
v5.0.34,2025-07-24 12:37:51,04ab4e7,Version 5.0.24-SNAPSHOT,quickbuild,<EMAIL>,2025-05-29 17:45:04,,task
v5.0.34,2025-07-24 12:37:51,20840e1,Version 5.0.23,quickbuild,<EMAIL>,2025-05-29 17:39:05,,task
v5.0.34,2025-07-24 12:37:51,cfff0cd,Tpm update - fix tests,Milan Černil,<EMAIL>,2025-05-29 17:23:42,,task
v5.0.34,2025-07-24 12:37:51,de6de16,"*********-8553: TPM 6.8.1, fix enrichers",Nikolas Charalambidis,<EMAIL>,2025-05-29 13:11:24,,bugfix
v5.0.34,2025-07-24 12:37:51,5f94012,"*********-23 - rework inapp for iOs *********-23 - rework inapp for iOs, merge with develop",Jakub Jirsa,<EMAIL>,2025-05-29 09:41:28,,task
v5.0.34,2025-07-24 12:37:51,914222e,CID: fix UAT tags,Jan Dockal,<EMAIL>,2025-05-28 11:37:33,,bugfix
v5.0.34,2025-07-24 12:37:51,5fd441a,fixup! CID: feature cms migrations,Jan Dockal,<EMAIL>,2025-05-28 11:35:29,,feature
v5.0.34,2025-07-24 12:37:51,475492f,Merged in bugfix/*********-8006/genCardMappingFix (pull request #1151),Nikolas Charalambidis,<EMAIL>,2025-05-28 10:21:14,bugfix/*********-8006/genCardMappingFix,bugfix
v5.0.34,2025-07-24 12:37:51,22c641a,*********-8561 Update GDS to 3.2.X - gas_appuser password 'a' set,Milan Černil,<EMAIL>,2025-05-28 09:04:11,,task
v5.0.34,2025-07-24 12:37:51,26be797,"CID: 5.0.22 (Finshape DEV, VB INT2, VB UAT2)",Nikolas Charalambidis,<EMAIL>,2025-05-27 05:02:12,,cid
v5.0.34,2025-07-24 12:37:51,af21019,Version 5.0.23-SNAPSHOT,quickbuild,<EMAIL>,2025-05-27 04:35:34,,task
v5.0.34,2025-07-24 12:37:51,b8bc973,Version 5.0.22,quickbuild,<EMAIL>,2025-05-27 04:29:51,,task
v5.0.34,2025-07-24 12:37:51,501452b,Merged in feature/4.2/gds-update (pull request #1147),Nikolas Charalambidis,<EMAIL>,2025-05-27 04:25:33,feature/4.2/gds-update,task
v5.0.34,2025-07-24 12:37:51,8ae9981,Merged in feature/*********-8557/eligibilityChanges (pull request #1145),Nikolas Charalambidis,<EMAIL>,2025-05-27 04:25:32,feature/*********-8557/eligibilityChanges,feature
v5.0.34,2025-07-24 12:37:51,f7a5889,Merged in bugfix/*********-483/p2pEnrichingFixPaymentFee (pull request #1149),Nikolas Charalambidis,<EMAIL>,2025-05-26 15:50:22,bugfix/*********-483/p2pEnrichingFixPaymentFee,bugfix
v5.0.34,2025-07-24 12:37:51,9fb20f4,Merged in feature/*********-484/npeFixP2PFeeEnriching (pull request #1148),Nikolas Charalambidis,<EMAIL>,2025-05-26 15:48:18,feature/*********-484/npeFixP2PFeeEnriching,feature
v5.0.34,2025-07-24 12:37:51,69d173d,*********-8562: GEN 25.4.2,Nikolas Charalambidis,<EMAIL>,2025-05-26 12:46:46,,task
v5.0.34,2025-07-24 12:37:51,25f6a22,Merged in feature4.2/deploy-business-config (pull request #1142),Milan Černil,<EMAIL>,2025-05-26 07:41:07,feature4.2/deploy-business-config,feature
v5.0.34,2025-07-24 12:37:51,267309d,VISA properties - enabled proxy,Milan Černil,<EMAIL>,2025-05-23 10:43:56,,task
v5.0.34,2025-07-24 12:37:51,fa3fba6,Merged in fix/e2e (pull request #1137),Milan Černil,<EMAIL>,2025-05-23 10:30:00,fix/e2e,bugfix
v5.0.34,2025-07-24 12:37:51,e658b09,*********-8547 - fix loading data from RTP in enricher,Jakub Jirsa,<EMAIL>,2025-05-23 10:22:15,,bugfix
v5.0.34,2025-07-24 12:37:51,2733a18,*********-7814 - fix statements fonts,Jakub Jirsa,<EMAIL>,2025-05-23 10:22:15,,bugfix
v5.0.34,2025-07-24 12:37:51,84729aa,Add custom java truststore to GAAS,Jan Holec,<EMAIL>,2025-05-22 12:46:33,,feature
v5.0.34,2025-07-24 12:37:51,b3844fc,"*********-8151 - add scheduled task, which deleted expired RtpRequests, fix refuse/cancel rtp",Jakub Jirsa,<EMAIL>,2025-05-21 13:40:06,,task
v5.0.34,2025-07-24 12:37:51,688ded4,TOOL-0000 fix cid configuration for IPS,Jakub Jirsa,<EMAIL>,2025-05-21 13:40:06,,bugfix
v5.0.34,2025-07-24 12:37:51,0c1795a,Merged in bugfix/*********-8507/happyHourXcrFix (pull request #1135),Nikolas Charalambidis,<EMAIL>,2025-05-21 09:26:19,bugfix/*********-8507/happyHourXcrFix,bugfix
v5.0.34,2025-07-24 12:37:51,190f423,Merged in bugfix/*********-8062/59BeneficiaryCustomer (pull request #1133),Nikolas Charalambidis,<EMAIL>,2025-05-21 04:16:53,bugfix/*********-8062/59BeneficiaryCustomer,bugfix
v5.0.34,2025-07-24 12:37:51,e11e3c1,Merged in feature/*********-8304/mockLinks2 (pull request #1134),Nikolas Charalambidis,<EMAIL>,2025-05-21 04:16:43,feature/*********-8304/mockLinks2,feature
v5.0.34,2025-07-24 12:37:51,e74170a,"CID: 5.0.21 (Finshape DEV, VB INT2, VB UAT2)",Nikolas Charalambidis,<EMAIL>,2025-05-20 14:57:23,,cid
v5.0.34,2025-07-24 12:37:51,34a700c,Version 5.0.22-SNAPSHOT,quickbuild,<EMAIL>,2025-05-20 13:28:46,,task
v5.0.34,2025-07-24 12:37:51,cabd374,Version 5.0.21,quickbuild,<EMAIL>,2025-05-20 13:22:41,,task
v5.0.34,2025-07-24 12:37:51,8475118,Merged in bugfix/*********-15/codeAlpha3Fix (pull request #1132),Nikolas Charalambidis,<EMAIL>,2025-05-20 13:06:56,bugfix/*********-15/codeAlpha3Fix,bugfix
v5.0.34,2025-07-24 12:37:51,4644f13,Merged in e2e-test-fix (pull request #1131),Milan Černil,<EMAIL>,2025-05-20 11:49:32,e2e-test-fix,bugfix
v5.0.34,2025-07-24 12:37:51,f929e1b,Merged in feature/NOJIRA/vb-ca-certificate (pull request #1130),Jan Holec,<EMAIL>,2025-05-20 11:00:38,feature/NOJIRA/vb-ca-certificate,feature
v5.0.34,2025-07-24 12:37:51,fa6dda3,*********-8353: Adds lastMovementDate,Nikolas Charalambidis,<EMAIL>,2025-05-20 10:19:44,,feature
v5.0.34,2025-07-24 12:37:51,60754eb,*********-8303: Fixes RabbitMQ connection on Finshape environments,Nikolas Charalambidis,<EMAIL>,2025-05-19 23:49:38,,bugfix
v5.0.34,2025-07-24 12:37:51,76fa07d,Merged in bugfix/*********-7943/retailEurLimitFix (pull request #1128),Nikolas Charalambidis,<EMAIL>,2025-05-19 17:58:21,bugfix/*********-7943/retailEurLimitFix,bugfix
v5.0.34,2025-07-24 12:37:51,b933803,Merged in bugfix/*********-8133/shortenName (pull request #1126),Nikolas Charalambidis,<EMAIL>,2025-05-19 17:58:20,bugfix/*********-8133/shortenName,bugfix
v5.0.34,2025-07-24 12:37:51,bffd764,Merged in fix/4.2/*********-8474/freeze-unfreeze-card (pull request #1127),Milan Černil,<EMAIL>,2025-05-19 16:46:44,fix/4.2/*********-8474/freeze-unfreeze-card,bugfix
v5.0.34,2025-07-24 12:37:51,44549e0,Merged in bugfix/*********-15/ownerEntityAttributeMatchValidatorFix (pull request #1129),Nikolas Charalambidis,<EMAIL>,2025-05-19 16:36:17,bugfix/*********-15/ownerEntityAttributeMatchValidatorFix,bugfix
v5.0.34,2025-07-24 12:37:51,1872402,Version 5.0.21-SNAPSHOT,quickbuild,<EMAIL>,2025-05-19 07:17:01,,task
v5.0.34,2025-07-24 12:37:51,d72a807,Version 5.0.20,quickbuild,<EMAIL>,2025-05-19 07:11:38,,task
v5.0.34,2025-07-24 12:37:51,d38e85e,Merged in feature/*********-8471/pushingFlag (pull request #1124),Nikolas Charalambidis,<EMAIL>,2025-05-19 06:20:24,feature/*********-8471/pushingFlag,feature
v5.0.34,2025-07-24 12:37:51,4670253,Merged in feature/*********-15/visaPhoneNumberFix (pull request #1125),Nikolas Charalambidis,<EMAIL>,2025-05-19 06:15:05,feature/*********-15/visaPhoneNumberFix,feature
v5.0.34,2025-07-24 12:37:51,5887541,Merged in feature/*********-15/codeAlpha3 (pull request #1122),Nikolas Charalambidis,<EMAIL>,2025-05-16 12:48:36,feature/*********-15/codeAlpha3,feature
v5.0.34,2025-07-24 12:37:51,160e997,CID: CMS latest migrations,Jan Dockal,<EMAIL>,2025-05-16 10:30:22,,cid
v5.0.34,2025-07-24 12:37:51,ed7ccee,Merged in cid-apple-app-site-association (pull request #1026),Jan Dočkal,<EMAIL>,2025-05-16 09:54:48,cid-apple-app-site-association,cid
v5.0.34,2025-07-24 12:37:51,f265e11,CID: Fix versions of neo ib-web a login,Jan Dockal,<EMAIL>,2025-05-15 22:52:17,,bugfix
v5.0.34,2025-07-24 12:37:51,ceaac5e,Merged in bugfix/*********-7282/mockFixDevelop (pull request #1121),Nikolas Charalambidis,<EMAIL>,2025-05-15 15:50:07,bugfix/*********-7282/mockFixDevelop,bugfix
v5.0.34,2025-07-24 12:37:51,ac2c0f6,Merged in fix/4.2/*********-8444/new-card-request-error (pull request #1116),Milan Černil,<EMAIL>,2025-05-15 15:03:38,fix/4.2/*********-8444/new-card-request-error,feature
v5.0.34,2025-07-24 12:37:51,32b6b26,Merged in fix/cid/terraform-cloud-config (pull request #1119),Roman Knotek,<EMAIL>,2025-05-15 14:34:54,fix/cid/terraform-cloud-config,bugfix
v5.0.34,2025-07-24 12:37:51,d387dfa,Merged in feature/*********-8187/isHappyHourFlag (pull request #1115),Nikolas Charalambidis,<EMAIL>,2025-05-15 11:50:08,feature/*********-8187/isHappyHourFlag,feature
v5.0.34,2025-07-24 12:37:51,241e9c4,Business Configuration - fix output and input folders,Milan Černil,<EMAIL>,2025-05-15 11:27:04,,bugfix
v5.0.34,2025-07-24 12:37:51,c6b8bdc,"CID: 5.0.19 (VB INT2, VB UAT2)",Nikolas Charalambidis,<EMAIL>,2025-05-15 07:13:43,,cid
v5.0.34,2025-07-24 12:37:51,26445ca,Version 5.0.20-SNAPSHOT,quickbuild,<EMAIL>,2025-05-14 13:23:29,,task
v5.0.34,2025-07-24 12:37:51,fe9a030,Version 5.0.19,quickbuild,<EMAIL>,2025-05-14 13:17:39,,task
v5.0.34,2025-07-24 12:37:51,696d01b,Merged in feature/*********-393/visaEntityId (pull request #1114),Nikolas Charalambidis,<EMAIL>,2025-05-14 13:09:45,feature/*********-393/visaEntityId,feature
v5.0.34,2025-07-24 12:37:51,347a22b,Merged in fix/4.2/*********-7877/account-detail (pull request #1113),Milan Černil,<EMAIL>,2025-05-14 11:13:03,fix/4.2/*********-7877/account-detail,bugfix
v5.0.34,2025-07-24 12:37:51,ba9307f,*********-8302 - fix remittance information for type1 print,Jakub Jirsa,<EMAIL>,2025-05-14 10:49:51,,bugfix
v5.0.34,2025-07-24 12:37:51,f3e989f,Merged in feature/*********-15/visaBrunoCollections (pull request #1112),Nikolas Charalambidis,<EMAIL>,2025-05-14 08:35:45,feature/*********-15/visaBrunoCollections,feature
v5.0.34,2025-07-24 12:37:51,80df30a,Merged in feature/*********-15/visaBodyEncryptionFix (pull request #1111),Nikolas Charalambidis,<EMAIL>,2025-05-14 07:19:16,feature/*********-15/visaBodyEncryptionFix,feature
v5.0.34,2025-07-24 12:37:51,aa59773,Merged in bugfix/*********-7641/thub1.0.0 (pull request #1110),Nikolas Charalambidis,<EMAIL>,2025-05-14 07:18:21,bugfix/*********-7641/thub1.0.0,bugfix
v5.0.34,2025-07-24 12:37:51,be8463c,Merged in feature/*********-15/maskVisaHeaders (pull request #1107),Nikolas Charalambidis,<EMAIL>,2025-05-13 12:40:07,feature/*********-15/maskVisaHeaders,feature
v5.0.34,2025-07-24 12:37:51,d785285,rychla oprava duplicity v overrides,jan_manina,<EMAIL>,2025-05-13 11:06:27,,task
v5.0.34,2025-07-24 12:37:51,5d73af6,"CID: 5.0.18 (VB INT2, VB UAT2)",Nikolas Charalambidis,<EMAIL>,2025-05-13 10:49:12,,cid
v5.0.34,2025-07-24 12:37:51,1c051b0,Version 5.0.19-SNAPSHOT,quickbuild,<EMAIL>,2025-05-13 10:44:25,,task
v5.0.34,2025-07-24 12:37:51,019ac0e,Version 5.0.18,quickbuild,<EMAIL>,2025-05-13 10:38:55,,task
v5.0.34,2025-07-24 12:37:51,0b83700,"*********-7900 - add filling fee into paymentApi, cherry-pick from 4.2 and resolve conflicts",Jakub Jirsa,<EMAIL>,2025-05-13 10:35:54,,feature
v5.0.34,2025-07-24 12:37:51,56be3e4,Merged in feature/*********-15/visaP2PAliasCidFix (pull request #1106),Nikolas Charalambidis,<EMAIL>,2025-05-13 10:33:00,feature/*********-15/visaP2PAliasCidFix,feature
v5.0.34,2025-07-24 12:37:51,ff74cc2,Merged in feature/*********-15/phoneNumberFormatted (pull request #1104),Nikolas Charalambidis,<EMAIL>,2025-05-13 06:23:40,feature/*********-15/phoneNumberFormatted,feature
v5.0.34,2025-07-24 12:37:51,940a773,*********-7993: Updates BSC Commons to 7.6.0 for custom BE components - E2E fix,Nikolas Charalambidis,<EMAIL>,2025-05-12 16:02:07,,task
v5.0.34,2025-07-24 12:37:51,2fd4a0a,Version 5.0.18-SNAPSHOT (substituting partial failure of #1560 Jenkins build of develop branch),Nikolas Charalambidis,<EMAIL>,2025-05-12 15:55:33,,task
v5.0.34,2025-07-24 12:37:51,2919fa3,*********-7011 - fix mapping for sender bank country,Jakub Jirsa,<EMAIL>,2025-05-12 15:28:31,,bugfix
v5.0.34,2025-07-24 12:37:51,cd2fc74,Merged in feature/*********-15/aliasInquiryFixes (pull request #1103),Nikolas Charalambidis,<EMAIL>,2025-05-12 15:17:33,feature/*********-15/aliasInquiryFixes,feature
v5.0.34,2025-07-24 12:37:51,106dd93,Merged in fix/4.2/*********-8415/GDE-message-processing-reconfiguration (pull request #1097),Milan Černil,<EMAIL>,2025-05-12 14:28:49,fix/4.2/*********-8415/GDE-message-processing-reconfiguration,bugfix
v5.0.34,2025-07-24 12:37:51,24cf3ce,Merged in bugfix/*********-7939/happyHourTotalAmountFix (pull request #1099),Nikolas Charalambidis,<EMAIL>,2025-05-12 11:03:37,bugfix/*********-7939/happyHourTotalAmountFix,bugfix
v5.0.34,2025-07-24 12:37:51,6343bc3,Merged in feature/*********-7993/bscCommonsCustomComponents7.60 (pull request #1102),Nikolas Charalambidis,<EMAIL>,2025-05-12 11:03:27,feature/*********-7993/bscCommonsCustomComponents7.60,feature
v5.0.34,2025-07-24 12:37:51,cc44316,Merged in feature/filebeat-ssl_console-logging-level (pull request #1091),Ján Manina,<EMAIL>,2025-05-12 10:47:57,feature/filebeat-ssl_console-logging-level,feature
v5.0.34,2025-07-24 12:37:51,808d1a5,NOJIRA-release 4.2.15,Jakub Jirsa,<EMAIL>,2025-05-06 15:43:52,,task
v5.0.34,2025-07-24 12:37:51,9eeeadb,Version 5.0.17-SNAPSHOT,quickbuild,<EMAIL>,2025-05-06 15:30:00,,task
v5.0.34,2025-07-24 12:37:51,037d877,Version 5.0.16,quickbuild,<EMAIL>,2025-05-06 15:23:25,,task
v5.0.34,2025-07-24 12:37:51,a84e031,"*********-23 In app provisioning (Apple, Google) - BE",Jakub Jirsa,<EMAIL>,2025-05-06 13:44:33,,task
v5.0.34,2025-07-24 12:37:51,7fa2fe9,Merged in feature/*********-8280/CARDS-display-of-currency-for-additional-cardholder (pull request #1094),Milan Černil,<EMAIL>,2025-05-06 13:35:08,feature/*********-8280/CARDS-display-of-currency-for-additional-cardholder,feature
v5.0.34,2025-07-24 12:37:51,0c69211,CID: web - lock 1B component versions,Jan Dockal,<EMAIL>,2025-05-05 14:59:54,,cid
v5.0.34,2025-07-24 12:37:51,caa5e72,Merged in fix/*********-7282/cid-rabbitmq (pull request #1032),Jan Holec,<EMAIL>,2025-05-05 13:28:30,fix/*********-7282/cid-rabbitmq,bugfix
v5.0.34,2025-07-24 12:37:51,b9a0848,Merged in feature/NOJIRA/cid-gaas-fix (pull request #1090),Jan Holec,<EMAIL>,2025-05-05 12:43:35,feature/NOJIRA/cid-gaas-fix,feature
v5.0.34,2025-07-24 12:37:51,1f8c22b,Merged in bugfix/*********-7845/partnerNameMappingFIx (pull request #1089),Nikolas Charalambidis,<EMAIL>,2025-05-05 10:52:42,bugfix/*********-7845/partnerNameMappingFIx,bugfix
v5.0.34,2025-07-24 12:37:51,9578364,"CID 4.2.14 INT, TEST, UAT",Milan Černil,<EMAIL>,2025-05-02 20:58:58,,cid
v5.0.34,2025-07-24 12:37:51,593d013,Version 5.0.16-SNAPSHOT,quickbuild,<EMAIL>,2025-05-02 14:31:01,,task
v5.0.34,2025-07-24 12:37:51,9f9dabd,Version 5.0.15,quickbuild,<EMAIL>,2025-05-02 14:25:20,,task
v5.0.34,2025-07-24 12:37:51,16731d5,BRUNO - push server config II,Milan Černil,<EMAIL>,2025-05-02 14:13:10,,cid
v5.0.34,2025-07-24 12:37:51,72b478c,BRUNO - push server config,Milan Černil,<EMAIL>,2025-05-02 14:13:10,,cid
v5.0.34,2025-07-24 12:37:51,b57bae8,Merged in feature/disable-filebeat-for-designer (pull request #1083),Ján Manina,<EMAIL>,2025-05-02 14:13:10,feature/disable-filebeat-for-designer,feature
v5.0.34,2025-07-24 12:37:51,467b64b,*********-7836 - fix template doc70,Jakub Jirsa,<EMAIL>,2025-05-02 13:27:34,,bugfix
v5.0.34,2025-07-24 12:37:51,e471ac1,Merged in fix/ea-oauth (pull request #1073),Tomáš Lipovský,<EMAIL>,2025-05-02 12:54:14,fix/ea-oauth,bugfix
v5.0.34,2025-07-24 12:37:51,b096fe0,Merged in feature/*********-8062/50PAYERACCOUNT (pull request #1087),Nikolas Charalambidis,<EMAIL>,2025-05-02 10:35:13,feature/*********-8062/50PAYERACCOUNT,feature
v5.0.34,2025-07-24 12:37:51,82628fc,filebeat+prometheus disabled for fs envs,jan_manina,<EMAIL>,2025-05-02 09:36:16,,task
v5.0.34,2025-07-24 12:37:51,b5e0123,release version 4.2.8 -> 4.2.12,jan_manina,<EMAIL>,2025-05-02 09:36:16,,task
v5.0.34,2025-07-24 12:37:51,eee397a,Merged in feature/added-monitoring-prometheus-filebeat (pull request #1022),Ján Manina,<EMAIL>,2025-05-02 09:36:15,feature/added-monitoring-prometheus-filebeat,feature
v5.0.34,2025-07-24 12:37:51,cae152b,Merged in task/4.2/*********-8316/3DS-deep-link-adjustment (pull request #1084),Milan Černil,<EMAIL>,2025-05-02 09:07:29,task/4.2/*********-8316/3DS-deep-link-adjustment,task
v5.0.34,2025-07-24 12:37:51,0c206b6,"*********-7832 - mirror fixes in templates, add RU properties to fix nullpointer prints, fix resource-budle-for-csv",Jakub Jirsa,<EMAIL>,2025-04-30 23:36:33,,feature
v5.0.34,2025-07-24 12:37:51,538bd53,CID: 4.2.13 (VB INT),Nikolas Charalambidis,<EMAIL>,2025-04-30 18:17:17,,cid
v5.0.34,2025-07-24 12:37:51,27423ca,FE Lock docker versions,Jan Dockal,<EMAIL>,2025-04-30 14:42:16,,task
v5.0.34,2025-07-24 12:37:51,1dddfa3,*********-7814 - fix translates,Jakub Jirsa,<EMAIL>,2025-04-30 11:42:17,,bugfix
v5.0.34,2025-07-24 12:37:51,9940e08,Merged in feature/*********-8304/mockLinks (pull request #1082),Nikolas Charalambidis,<EMAIL>,2025-04-30 10:52:30,feature/*********-8304/mockLinks,feature
v5.0.34,2025-07-24 12:37:51,9b35486,Merged in feature/*********-8235/e2eFix (pull request #1081),Nikolas Charalambidis,<EMAIL>,2025-04-30 10:51:58,feature/*********-8235/e2eFix,feature
v5.0.34,2025-07-24 12:37:51,4e625e1,Merged in bugfix/*********-7939/happyHourTimezonesFix (pull request #1078),Nikolas Charalambidis,<EMAIL>,2025-04-30 10:51:49,bugfix/*********-7939/happyHourTimezonesFix,bugfix
v5.0.34,2025-07-24 12:37:51,f4990fc,*********-7719 - fix template doc20,Jakub Jirsa,<EMAIL>,2025-04-29 21:56:54,,bugfix
v5.0.34,2025-07-24 12:37:51,4766579,"CID: 4.2.12 - Finshape TEST, VB INT",Nikolas Charalambidis,<EMAIL>,2025-04-29 12:44:43,,cid
v5.0.34,2025-07-24 12:37:51,fc4bc80,Version 5.0.15-SNAPSHOT,quickbuild,<EMAIL>,2025-04-29 12:23:45,,task
v5.0.34,2025-07-24 12:37:51,b8a767f,Version 5.0.14,quickbuild,<EMAIL>,2025-04-29 12:18:24,,task
v5.0.34,2025-07-24 12:37:51,a328b72,Merged in feature/*********-8108/openProductEligibility (pull request #1076),Nikolas Charalambidis,<EMAIL>,2025-04-29 11:53:50,feature/*********-8108/openProductEligibility,feature
v5.0.34,2025-07-24 12:37:51,25b2427,Merged in bugifx/*********-7676/jenkinsfileFix (pull request #1075),Nikolas Charalambidis,<EMAIL>,2025-04-29 11:53:49,bugifx/*********-7676/jenkinsfileFix,bugfix
v5.0.34,2025-07-24 12:37:51,a4196ee,*********-7560 - optimize retry check status for IPS payments,Jakub Jirsa,<EMAIL>,2025-04-29 11:53:44,,task
v5.0.34,2025-07-24 12:37:51,52c6691,INT2 & UAT2 password update,Milan Černil,<EMAIL>,2025-04-28 15:59:44,,task
v5.0.34,2025-07-24 12:37:51,73569f7,Merged in feature/*********-7872/removeRemittanceInformationEnrichingForTrgDomesticPO (pull request #1071),Nikolas Charalambidis,<EMAIL>,2025-04-28 07:19:27,feature/*********-7872/removeRemittanceInformationEnrichingForTrgDomesticPO,feature
v5.0.34,2025-07-24 12:37:51,643485a,Merged in feature/*********-8047/reissueCardProductEligibilityValidator (pull request #1068),Nikolas Charalambidis,<EMAIL>,2025-04-27 18:07:12,feature/*********-8047/reissueCardProductEligibilityValidator,feature
v5.0.34,2025-07-24 12:37:51,294f398,Merged in feature/NOJIRA/jenkinsfile (pull request #997),Nikolas Charalambidis,<EMAIL>,2025-04-27 18:05:39,feature/NOJIRA/jenkinsfile,feature
v5.0.34,2025-07-24 12:37:51,b2f6c46,Merged in fix/e2e-test (pull request #1067),Milan Černil,<EMAIL>,2025-04-25 21:23:39,fix/e2e-test,bugfix
v5.0.34,2025-07-24 12:37:51,89320cf,Merged in vb-ldap (pull request #1069),Tomáš Lipovský,<EMAIL>,2025-04-25 20:52:33,vb-ldap,task
v5.0.34,2025-07-24 12:37:51,44db664,Version 5.0.14-SNAPSHOT,quickbuild,<EMAIL>,2025-04-25 15:39:30,,task
v5.0.34,2025-07-24 12:37:51,11f2627,Version 5.0.13,quickbuild,<EMAIL>,2025-04-25 15:29:12,,task
v5.0.34,2025-07-24 12:37:51,f732b7f,build fix,Milan Černil,<EMAIL>,2025-04-25 15:16:23,,bugfix
v5.0.34,2025-07-24 12:37:51,c371962,Bruno - financials and nonfinancials removal after rename,Milan Černil,<EMAIL>,2025-04-25 14:16:16,,task
v5.0.34,2025-07-24 12:37:51,070e1e7,Merged in fix/4.2/*********-7519/IPS-Movements-masked-partner-name (pull request #1066),Milan Černil,<EMAIL>,2025-04-25 14:16:15,fix/4.2/*********-7519/IPS-Movements-masked-partner-name,bugfix
v5.0.34,2025-07-24 12:37:51,ac83a57,Merged in fix/4.2/*********-8176/Closed_card_flow-error (pull request #1065),Milan Černil,<EMAIL>,2025-04-25 14:16:15,fix/4.2/*********-8176/Closed_card_flow-error,bugfix
v5.0.34,2025-07-24 12:37:51,618dca1,CID INT 4.2.9,Milan Černil,<EMAIL>,2025-04-25 14:15:20,,cid
v5.0.34,2025-07-24 12:37:51,275b86e,Merged in feature/*********-8096/documentUploadHandlingImprovement (pull request #1064),Nikolas Charalambidis,<EMAIL>,2025-04-25 11:07:05,feature/*********-8096/documentUploadHandlingImprovement,feature
v5.0.34,2025-07-24 12:37:51,f45fb4b,Version 5.0.13-SNAPSHOT,quickbuild,<EMAIL>,2025-04-24 15:55:38,,task
v5.0.34,2025-07-24 12:37:51,754c73e,Version 5.0.12,quickbuild,<EMAIL>,2025-04-24 15:45:16,,task
v5.0.34,2025-07-24 12:37:51,cfe789e,CID: TEST: 4.2.9,Nikolas Charalambidis,<EMAIL>,2025-04-24 15:38:15,,cid
v5.0.34,2025-07-24 12:37:51,0550a6d,*********-6859: Remaps card max limits,Nikolas Charalambidis,<EMAIL>,2025-04-24 15:06:51,,task
v5.0.34,2025-07-24 12:37:51,f035cc4,Merged in feature/4.2/*********-8075/BIC-shortcode (pull request #1063),Milan Černil,<EMAIL>,2025-04-24 14:30:29,feature/4.2/*********-8075/BIC-shortcode,feature
v5.0.34,2025-07-24 12:37:51,44508ec,Merged in fix/*********-8087/releasing-payment-limit (pull request #1051),Jakub Jirsa,<EMAIL>,2025-04-24 14:23:55,fix/*********-8087/releasing-payment-limit,bugfix
v5.0.34,2025-07-24 12:37:51,1589d25,*********-8185 Client data mapping configuration - config,Milan Černil,<EMAIL>,2025-04-24 14:11:34,,cid
v5.0.34,2025-07-24 12:37:51,ed2865f,*********-7319 - change IPS limits,Jakub Jirsa,<EMAIL>,2025-04-24 12:11:10,,task
v5.0.34,2025-07-24 12:37:51,f19ccd5,Merged in feature/*********-8167/openProductFeeAmountAndCurrencz (pull request #1056),Nikolas Charalambidis,<EMAIL>,2025-04-24 07:53:41,feature/*********-8167/openProductFeeAmountAndCurrencz,feature
v5.0.34,2025-07-24 12:37:51,cc3b44c,Merged in feature/*********-7971/happyHourExceededTranslationPlaceholder (pull request #1058),Nikolas Charalambidis,<EMAIL>,2025-04-24 07:53:15,feature/*********-7971/happyHourExceededTranslationPlaceholder,feature
v5.0.34,2025-07-24 12:37:51,a630b56,Merged in feature/NOJIRA/generateDbosDomainRemoveDeprecations (pull request #1057),Nikolas Charalambidis,<EMAIL>,2025-04-24 07:53:13,feature/NOJIRA/generateDbosDomainRemoveDeprecations,feature
v5.0.34,2025-07-24 12:37:51,fa48c52,Version 5.0.12-SNAPSHOT,quickbuild,<EMAIL>,2025-04-23 13:26:04,,task
v5.0.34,2025-07-24 12:37:51,6d47bb3,Version 5.0.11,quickbuild,<EMAIL>,2025-04-23 13:16:09,,task
v5.0.34,2025-07-24 12:37:51,491a5bd,NOJIRA - Checkstyle fix,Milan Černil,<EMAIL>,2025-04-23 13:14:53,,bugfix
v5.0.34,2025-07-24 12:37:51,f78d133,*********-7712 _ fix template doc12,Jakub Jirsa,<EMAIL>,2025-04-22 13:33:15,,bugfix
v5.0.34,2025-07-24 12:37:51,203e31a,*********-7282: Added missing implements of CbsRestController,Nikolas Charalambidis,<EMAIL>,2025-04-22 12:09:27,,feature
v5.0.34,2025-07-24 12:37:51,ee91e96,Merged in feature/*********-7993/improveRestLogging (pull request #1053),Nikolas Charalambidis,<EMAIL>,2025-04-22 10:49:12,feature/*********-7993/improveRestLogging,feature
v5.0.34,2025-07-24 12:37:51,c7c45f0,*********-6859: Merge fix,Nikolas Charalambidis,<EMAIL>,2025-04-22 09:04:49,,bugfix
v5.0.34,2025-07-24 12:37:51,3745cfa,Merged in feature/4.2/*********-6859/WAY4-Max-value-for-card-limit (pull request #1044),Nikolas Charalambidis,<EMAIL>,2025-04-22 08:56:24,feature/4.2/*********-6859/WAY4-Max-value-for-card-limit,feature
v5.0.34,2025-07-24 12:37:51,693cd5e,Merged in bugfix/*********-8160/forexListenerForSameCurrencies (pull request #1052),Nikolas Charalambidis,<EMAIL>,2025-04-22 08:48:37,bugfix/*********-8160/forexListenerForSameCurrencies,bugfix
v5.0.34,2025-07-24 12:37:51,6afeb66,CID INT2 & UAT2 5.0.10,Milan Černil,<EMAIL>,2025-04-17 17:50:00,,cid
v5.0.34,2025-07-24 12:37:51,87b6570,CID INT & UAT 4.2.8,Milan Černil,<EMAIL>,2025-04-17 17:49:47,,cid
v5.0.34,2025-07-24 12:37:51,7c05267,Version 5.0.11-SNAPSHOT,quickbuild,<EMAIL>,2025-04-17 17:35:55,,task
v5.0.34,2025-07-24 12:37:51,de726e4,Version 5.0.10,quickbuild,<EMAIL>,2025-04-17 17:25:24,,task
v5.0.34,2025-07-24 12:37:51,ae03f83,Merged in feature/*********-8115/exb2.1.0 (pull request #1050),Nikolas Charalambidis,<EMAIL>,2025-04-17 17:13:49,feature/*********-8115/exb2.1.0,feature
v5.0.34,2025-07-24 12:37:51,185770e,Merged in feature/*********-8083/gdeMessageConcurrentUpdateHandling (pull request #1047),Nikolas Charalambidis,<EMAIL>,2025-04-17 17:13:44,feature/*********-8083/gdeMessageConcurrentUpdateHandling,task
v5.0.34,2025-07-24 12:37:51,2c3bf1a,Merged in fix/4.2/*********-7939/Own-accounts-trasfer-Happy-and-forex (pull request #1049),Milan Černil,<EMAIL>,2025-04-17 17:05:31,fix/4.2/*********-7939/Own-accounts-trasfer-Happy-and-forex,bugfix
v5.0.34,2025-07-24 12:37:51,427d864,BRUNO/reorganize collections,Jakub Jirsa,<EMAIL>,2025-04-17 12:02:24,,task
v5.0.34,2025-07-24 12:37:51,522426d,"*********-5746 - remove unsued scripts from bruno, add local env",Jakub Jirsa,<EMAIL>,2025-04-17 12:02:20,,feature
v5.0.34,2025-07-24 12:37:51,8b29fda,Merged in feature/*********-7282/e2eFixes2 (pull request #1042),Nikolas Charalambidis,<EMAIL>,2025-04-17 11:50:23,feature/*********-7282/e2eFixes2,feature
v5.0.34,2025-07-24 12:37:51,98b20c2,Merged in fix/4.2/*********-8024/Search-in-Movements (pull request #1045),Milan Černil,<EMAIL>,2025-04-17 09:59:46,fix/4.2/*********-8024/Search-in-Movements,bugfix
v5.0.34,2025-07-24 12:37:51,8c46ace,Merged in feature/4.2/*********-7961-GDE-optimization (pull request #1028),Milan Černil,<EMAIL>,2025-04-16 14:33:25,feature/4.2/*********-7961-GDE-optimization,feature
v5.0.34,2025-07-24 12:37:51,fa7fe64,CID: Fix wac2,Jan Dockal,<EMAIL>,2025-04-15 18:05:27,,bugfix
v5.0.34,2025-07-24 12:37:51,7aecf92,Merged in fix/4.2/*********-8012/3DS-fixes (pull request #1041),Milan Černil,<EMAIL>,2025-04-14 14:53:00,fix/4.2/*********-8012/3DS-fixes,bugfix
v5.0.34,2025-07-24 12:37:51,79b0ebc,Feature/4.2/*********-5746/migrate postman to bruno,Jakub Jirsa,<EMAIL>,2025-04-14 14:41:50,,feature
v5.0.34,2025-07-24 12:37:51,dda3ac4,"*********-7011 - fix reciept template, fix loading translated catalog values, fix floating in template",Jakub Jirsa,<EMAIL>,2025-04-14 14:29:28,,bugfix
v5.0.34,2025-07-24 12:37:51,7550abe,*********-7405 - fix validating dateFrom,Jakub Jirsa,<EMAIL>,2025-04-14 13:42:47,,bugfix
v5.0.34,2025-07-24 12:37:51,668589a,Feature/*********-7269/map ips movements reciepts to doc1 template,Jakub Jirsa,<EMAIL>,2025-04-14 11:19:14,,feature
v5.0.34,2025-07-24 12:37:51,0c1d923,Merged in fix/4.2/*********-7673/Own-account-transfer-acc-not-found (pull request #1039),Milan Černil,<EMAIL>,2025-04-14 10:18:48,fix/4.2/*********-7673/Own-account-transfer-acc-not-found,bugfix
v5.0.34,2025-07-24 12:37:51,82690ea,Version 5.0.10-SNAPSHOT,quickbuild,<EMAIL>,2025-04-11 17:27:35,,task
v5.0.34,2025-07-24 12:37:51,e2f1d54,Version 5.0.9,quickbuild,<EMAIL>,2025-04-11 17:17:38,,task
v5.0.34,2025-07-24 12:37:51,6b0a733,Merged in feature/*********-7282/e2eFixes (pull request #1033),Nikolas Charalambidis,<EMAIL>,2025-04-11 16:36:02,feature/*********-7282/e2eFixes,feature
v5.0.34,2025-07-24 12:37:51,7cbb8a7,CID: FE tags for DEV2,Jan Dockal,<EMAIL>,2025-04-10 16:38:49,,cid
v5.0.34,2025-07-24 12:37:51,85a0d70,Merged in feature/4.2/*********-7968-Card-replication-exception-catch (pull request #1031),Milan Černil,<EMAIL>,2025-04-10 15:07:06,feature/4.2/*********-7968-Card-replication-exception-catch,feature
v5.0.34,2025-07-24 12:37:51,061f479,Merged in feature/NOJIRA/uploadedDocumentCategoriesPaymentsFix (pull request #1030),Nikolas Charalambidis,<EMAIL>,2025-04-10 14:47:42,feature/NOJIRA/uploadedDocumentCategoriesPaymentsFix,feature
v5.0.34,2025-07-24 12:37:51,3cd6daa,"*********-7733 - fix amount value format, fir mapping Nr. field",Jakub Jirsa,<EMAIL>,2025-04-10 13:36:17,,bugfix
v5.0.34,2025-07-24 12:37:51,1c01c09,"*********-7044 - fix align in table, fix mapping of credit used, payment due",Jakub Jirsa,<EMAIL>,2025-04-10 13:36:15,,bugfix
v5.0.34,2025-07-24 12:37:51,5da7b90,Merged in feature/*********-7282/rabbitMqAndMovements (pull request #1015),Nikolas Charalambidis,<EMAIL>,2025-04-10 13:26:56,feature/*********-7282/rabbitMqAndMovements,feature
v5.0.34,2025-07-24 12:37:51,1c04b58,NOJIRA: CID.md,Nikolas Charalambidis,<EMAIL>,2025-04-10 07:28:55,,cid
v5.0.34,2025-07-24 12:37:51,e455b53,Version 5.0.9-SNAPSHOT,quickbuild,<EMAIL>,2025-04-10 06:51:31,,task
v5.0.34,2025-07-24 12:37:51,f3706ae,Version 5.0.8,quickbuild,<EMAIL>,2025-04-10 06:41:47,,task
v5.0.34,2025-07-24 12:37:51,bd9124d,Branch replication - build fix,Milan Černil,<EMAIL>,2025-04-09 14:50:10,,bugfix
v5.0.34,2025-07-24 12:37:51,e5c2aae,Branch replication - basic payload validation,Milan Černil,<EMAIL>,2025-04-09 14:44:02,,task
v5.0.34,2025-07-24 12:37:51,9f06526,CID: Lock CMS migration versions,Jan Dockal,<EMAIL>,2025-04-09 14:09:16,,cid
v5.0.34,2025-07-24 12:37:51,12b804c,Merged in feature/*********-15/adjustStorePanMock (pull request #1017),Nikolas Charalambidis,<EMAIL>,2025-04-09 13:00:22,feature/*********-15/adjustStorePanMock,feature
v5.0.34,2025-07-24 12:37:51,b27aa22,Merged in feature/*********-7599/uploadFileWithRrn (pull request #1020),Nikolas Charalambidis,<EMAIL>,2025-04-09 10:34:52,feature/*********-7599/uploadFileWithRrn,feature
v5.0.34,2025-07-24 12:37:51,ae0d583,Merged in feature/*********-7589/salaryProjectUploadValueDate (pull request #1016),Nikolas Charalambidis,<EMAIL>,2025-04-09 10:34:49,feature/*********-7589/salaryProjectUploadValueDate,feature
v5.0.34,2025-07-24 12:37:51,cf7a793,Merged in feature/*********-7474/feeWIthPaymentType (pull request #1021),Nikolas Charalambidis,<EMAIL>,2025-04-09 10:34:26,feature/*********-7474/feeWIthPaymentType,feature
v5.0.34,2025-07-24 12:37:51,f26af1c,Merged in fix/4.2/*********/*********-7608-open-product-message-error (pull request #1024),Milan Černil,<EMAIL>,2025-04-09 10:00:22,fix/4.2/*********/*********-7608-open-product-message-error,bugfix
v5.0.34,2025-07-24 12:37:51,f9f751f,Merged in fix/4.2/*********-7701Payment-in-the-future (pull request #1023),Milan Černil,<EMAIL>,2025-04-09 10:00:18,fix/4.2/*********-7701Payment-in-the-future,bugfix
v5.0.34,2025-07-24 12:37:51,88272ce,*********-7880 - fix mapping payment type code for IPS intrabank payments,Jakub Jirsa,<EMAIL>,2025-04-09 09:49:16,,bugfix
v5.0.34,2025-07-24 12:37:51,ccb8412,Increase memory,Jan Dockal,<EMAIL>,2025-04-08 09:48:52,,task
v5.0.34,2025-07-24 12:37:51,02afe05,Merged in fix/*********-15/aliasInconsistencyErrorCodeTypoFix (pull request #1014),Nikolas Charalambidis,<EMAIL>,2025-04-07 08:22:41,fix/*********-15/aliasInconsistencyErrorCodeTypoFix,bugfix
v5.0.34,2025-07-24 12:37:51,14792d6,Merged in fix/NOJIRA/cid-rabbitmq (pull request #1013),Jan Holec,<EMAIL>,2025-04-07 08:00:13,fix/NOJIRA/cid-rabbitmq,bugfix
v5.0.34,2025-07-24 12:37:51,7b3f238,CID - finshape envs - rabbitmq queue host name,Milan Černil,<EMAIL>,2025-04-07 07:55:11,,cid
v5.0.34,2025-07-24 12:37:51,10daa47,Merged in web-cid (pull request #1011),Jan Dočkal,<EMAIL>,2025-04-04 20:40:45,web-cid,cid
v5.0.34,2025-07-24 12:37:51,2bbee46,Merged in fix/4.2/*********-3831/Account-balances-npe (pull request #1012),Milan Černil,<EMAIL>,2025-04-04 12:43:44,fix/4.2/*********-3831/Account-balances-npe,bugfix
v5.0.34,2025-07-24 12:37:51,cb8a4d8,"CID INT, TEST, UAT 4.2.6",Milan Černil,<EMAIL>,2025-04-03 14:18:16,,cid
v5.0.34,2025-07-24 12:37:51,b5d2941,NOJIRA - CID fixes,Milan Černil,<EMAIL>,2025-04-03 14:18:16,,bugfix
v5.0.34,2025-07-24 12:37:51,cf9570d,Version 5.0.8-SNAPSHOT,quickbuild,<EMAIL>,2025-04-03 14:06:17,,task
v5.0.34,2025-07-24 12:37:51,fa4b052,Version 5.0.7,quickbuild,<EMAIL>,2025-04-03 13:56:32,,task
v5.0.34,2025-07-24 12:37:51,5dd7a34,Merged in fix/4.2/*********-6847-CID-fixes (pull request #1001),Milan Černil,<EMAIL>,2025-04-03 12:53:00,fix/4.2/*********-6847-CID-fixes,bugfix
v5.0.34,2025-07-24 12:37:51,03bc18d,Merged in feature/4.2/*********-7742-BE-change-of-the-replication-of-relatedAccount-on-Movements (pull request #1010),Milan Černil,<EMAIL>,2025-04-03 12:53:00,feature/4.2/*********-7742-BE-change-of-the-replication-of-relatedAccount-on-Movements,feature
v5.0.34,2025-07-24 12:37:51,c3196a1,Merged in feature/*********-7587/Mask-partner-name-for-IPS-debit-movements (pull request #990),Milan Černil,<EMAIL>,2025-04-03 12:53:00,feature/*********-7587/Mask-partner-name-for-IPS-debit-movements,feature
v5.0.34,2025-07-24 12:37:51,ac5e768,*********-7400 - add filtering out Victoria bank servicer,Jakub Jirsa,<EMAIL>,2025-04-03 11:17:19,,feature
v5.0.34,2025-07-24 12:37:51,24555bd,"*********-7405 - adjust statementList templates to show """" instead of ""-"" or ""null"", handle potential NPE",Jakub Jirsa,<EMAIL>,2025-04-03 10:56:02,,task
v5.0.34,2025-07-24 12:37:51,a15d70c,*********-7703 - fix parsing RTP request,Jakub Jirsa,<EMAIL>,2025-04-03 10:56:00,,bugfix
v5.0.34,2025-07-24 12:37:51,8a27afe,*********-7703 - fix thub flow for ips intrabank,Jakub Jirsa,<EMAIL>,2025-04-03 10:55:58,,bugfix
v5.0.34,2025-07-24 12:37:51,90d83db,NOJIRA: *********-7710: Merge fix,Nikolas Charalambidis,<EMAIL>,2025-04-03 10:37:49,,bugfix
v5.0.34,2025-07-24 12:37:51,b0d0655,*********-7710: THUB 1.0.0-M.25,Nikolas Charalambidis,<EMAIL>,2025-04-03 10:32:45,,task
v5.0.34,2025-07-24 12:37:51,3190e77,*********-3831: Fixes available balances by subtracting locked amount,Nikolas Charalambidis,<EMAIL>,2025-04-02 09:28:03,,bugfix
v5.0.34,2025-07-24 12:37:51,36423d2,raised gaas mem limit to 3GB on vb-test env,jan_manina,<EMAIL>,2025-04-01 12:33:53,,task
v5.0.34,2025-07-24 12:37:51,5f11255,NOJIRA - update release version for vb-test Approved-by: Nikolas Charalambidis Approved-by: Milan Černil,Jakub Jirsa,<EMAIL>,2025-04-01 12:24:54,,task
v5.0.34,2025-07-24 12:37:51,bfd5602,*********-7155 - fix mapping creditor/debitor for incommint RPT,Jakub Jirsa,<EMAIL>,2025-04-01 12:24:49,,bugfix
v5.0.34,2025-07-24 12:37:51,6ddbebc,"*********-7155 - fix ips pain 13 credit operation processor, fix gds query",Jakub Jirsa,<EMAIL>,2025-04-01 12:24:42,,bugfix
v5.0.34,2025-07-24 12:37:51,00b9410,*********-7155 - fix ips pain 13 credit operation processor,Jakub Jirsa,<EMAIL>,2025-04-01 12:24:33,,bugfix
v5.0.34,2025-07-24 12:37:51,1c8d214,Merged in bugfix/*********-7661/fixT24PaymentJsonResponse (pull request #998),Nikolas Charalambidis,<EMAIL>,2025-04-01 11:49:06,bugfix/*********-7661/fixT24PaymentJsonResponse,bugfix
v5.0.34,2025-07-24 12:37:51,605647f,Merged in feature/*********-6847/cidAndConfigFixes (pull request #996),Nikolas Charalambidis,<EMAIL>,2025-04-01 10:15:22,feature/*********-6847/cidAndConfigFixes,feature
v5.0.34,2025-07-24 12:37:51,ef43900,*********-6847 fix of wrong tpm version deployment,Milan Černil,<EMAIL>,2025-03-31 15:39:10,,bugfix
v5.0.34,2025-07-24 12:37:51,f52504a,Merged in feature/*********-6847-readme (pull request #995),Milan Černil,<EMAIL>,2025-03-31 15:39:10,feature/*********-6847-readme,feature
v5.0.34,2025-07-24 12:37:51,d1e76bd,.gitignore,Nikolas Charalambidis,<EMAIL>,2025-03-31 11:17:10,,task
v5.0.34,2025-07-24 12:37:51,6fb656d,*********-368: VB Finshape API 1.4.7,Nikolas Charalambidis,<EMAIL>,2025-03-31 09:45:10,,task
v5.0.34,2025-07-24 12:37:51,2c4fda9,*********-6847 Create new INT2 environment & grafana - BE,Milan Černil,<EMAIL>,2025-03-29 13:19:13,,feature
v5.0.34,2025-07-24 12:37:51,5f437b9,Merged in feature/*********-7282/4.1/New-DEV-env-+-rabbitMQ (pull request #978),Milan Černil,<EMAIL>,2025-03-29 13:19:13,feature/*********-7282/4.1/New-DEV-env-+-rabbitMQ,feature
v5.0.34,2025-07-24 12:37:51,b8fe46f,Merged in feature/*********-6847/new-INT2-environments (pull request #968),Milan Černil,<EMAIL>,2025-03-29 13:17:53,feature/*********-6847/new-INT2-environments,feature
v5.0.34,2025-07-24 12:37:51,32a8d2d,Version 5.0.7-SNAPSHOT,quickbuild,<EMAIL>,2025-03-29 10:17:08,,task
v5.0.34,2025-07-24 12:37:51,6267097,Version 5.0.6,quickbuild,<EMAIL>,2025-03-29 10:08:00,,task
v5.0.34,2025-07-24 12:37:51,40641e4,Merged in feature/NOJIRA/5.0.6-SNAPSHOT (pull request #993),Nikolas Charalambidis,<EMAIL>,2025-03-29 09:45:31,feature/NOJIRA/5.0.6-SNAPSHOT,feature
v5.0.34,2025-07-24 12:37:51,8b03bac,Merged in bugfix/*********-345/5.0.4fixes (pull request #977),Nikolas Charalambidis,<EMAIL>,2025-03-28 11:05:11,bugfix/*********-345/5.0.4fixes,bugfix
v5.0.34,2025-07-24 12:37:51,4377ee3,*********-7512: OpenAPI fix,Nikolas Charalambidis,<EMAIL>,2025-03-28 10:56:29,,bugfix
v5.0.34,2025-07-24 12:37:51,883ac01,Merged in feature/*********-7512/replicationInfo (pull request #982),Nikolas Charalambidis,<EMAIL>,2025-03-28 10:53:12,feature/*********-7512/replicationInfo,feature
v5.0.34,2025-07-24 12:37:51,f689165,"*********-7237 - new thub flow for ips intrabank payment, rename liquibase script",Jakub Jirsa,<EMAIL>,2025-03-28 09:36:14,,feature
v5.0.34,2025-07-24 12:37:51,78de78d,Merged in NOJIRA/merge-ips-flow-from-4.1-to-develop (pull request #987),Nikolas Charalambidis,<EMAIL>,2025-03-27 15:24:22,NOJIRA/merge-ips-flow-from-4.1-to-develop,task
v5.0.34,2025-07-24 12:37:51,8767d05,NOJIRA - fix E2E test,Milan Černil,<EMAIL>,2025-03-27 10:20:17,,bugfix
v5.0.34,2025-07-24 12:37:51,b6aadac,Merged in feature/*********-7501/Update-Attachment-Handling-II (pull request #989),Milan Černil,<EMAIL>,2025-03-27 09:56:15,feature/*********-7501/Update-Attachment-Handling-II,task
v5.0.34,2025-07-24 12:37:51,475f263,Merged in feature/*********-7555/GAAS-&-GEN-hikari-pool (pull request #986),Milan Černil,<EMAIL>,2025-03-26 11:06:13,feature/*********-7555/GAAS-&-GEN-hikari-pool,feature
v5.0.34,2025-07-24 12:37:51,22ff184,Merged in feature/*********-7501/Update-Attachment-Handling-for-Swift-IntrabankFcyPO (pull request #984),Nikolas Charalambidis,<EMAIL>,2025-03-26 09:46:16,feature/*********-7501/Update-Attachment-Handling-for-Swift-IntrabankFcyPO,task
v5.0.34,2025-07-24 12:37:51,69da261,Merged in fix/*********-7430/Treasury-account-wrong-decimal-separator-on-detail (pull request #985),Milan Černil,<EMAIL>,2025-03-26 07:09:08,fix/*********-7430/Treasury-account-wrong-decimal-separator-on-detail,bugfix
v5.0.34,2025-07-24 12:37:51,ad737f8,Merged in fix/*********-7493/Reissue-card-CBS-Payload (pull request #980),Milan Černil,<EMAIL>,2025-03-24 14:43:16,fix/*********-7493/Reissue-card-CBS-Payload,bugfix
v5.0.34,2025-07-24 12:37:51,44a6a7d,Merged in feature/*********-7471/New-HSM-fun-configuration (pull request #974),Milan Černil,<EMAIL>,2025-03-24 14:33:43,feature/*********-7471/New-HSM-fun-configuration,feature
v5.0.34,2025-07-24 12:37:51,037711d,*********-347 GAAS update 15.2.1,Milan Černil,<EMAIL>,2025-03-24 14:30:32,,task
v5.0.34,2025-07-24 12:37:51,8b481ee,Version 5.0.5-SNAPSHOT,quickbuild,<EMAIL>,2025-03-24 05:33:30,,task
v5.0.34,2025-07-24 12:37:51,231afbf,Version 5.0.4,quickbuild,<EMAIL>,2025-03-24 05:23:14,,task
v5.0.34,2025-07-24 12:37:51,178c819,Merged in bugfix/nojira/spxOpenApiFix (pull request #975),Nikolas Charalambidis,<EMAIL>,2025-03-24 05:20:08,bugfix/nojira/spxOpenApiFix,bugfix
v5.0.34,2025-07-24 12:37:51,f3f013b,Approved-by: Nikolas Charalambidis Approved-by: Milan Černil,Jakub Jirsa,<EMAIL>,2025-03-23 22:32:57,,task
v5.0.34,2025-07-24 12:37:51,0751055,"*********-7011 - fix loading city from GDE, add city into statements mapping",Jakub Jirsa,<EMAIL>,2025-03-23 22:27:21,,feature
v5.0.34,2025-07-24 12:37:51,059caa3,*********-257 - add E2E test for card flow which are affected by Visa alias,Jakub Jirsa,<EMAIL>,2025-03-23 21:30:26,,feature
v5.0.34,2025-07-24 12:37:51,b00325b,Version 5.0.4-SNAPSHOT,quickbuild,<EMAIL>,2025-03-21 11:18:45,,task
v5.0.34,2025-07-24 12:37:51,f412d5d,Version 5.0.3,quickbuild,<EMAIL>,2025-03-21 11:10:00,,task
v5.0.34,2025-07-24 12:37:51,93c055f,Merged in feature/*********-266/acceptLanguage (pull request #945),Nikolas Charalambidis,<EMAIL>,2025-03-21 11:01:18,feature/*********-266/acceptLanguage,feature
v5.0.34,2025-07-24 12:37:51,818f46c,Merged in feature/*********-260/p2pPayment2 (pull request #969),Nikolas Charalambidis,<EMAIL>,2025-03-21 10:33:57,feature/*********-260/p2pPayment2,feature
v5.0.34,2025-07-24 12:37:51,0d84d5b,Merged in fix/*********-7011/Receipt-for-individuals-payment-between-accounts (pull request #970),Milan Černil,<EMAIL>,2025-03-21 09:24:36,fix/*********-7011/Receipt-for-individuals-payment-between-accounts,bugfix
v5.0.34,2025-07-24 12:37:51,0a5c3d3,Merged in feature/*********-260/p2pPayment (pull request #950),Nikolas Charalambidis,<EMAIL>,2025-03-21 04:06:09,feature/*********-260/p2pPayment,feature
v5.0.34,2025-07-24 12:37:51,0358b73,Merged in feature/*********-318/checkUserAliasFinetuning (pull request #967),Nikolas Charalambidis,<EMAIL>,2025-03-20 11:39:56,feature/*********-318/checkUserAliasFinetuning,feature
v5.0.34,2025-07-24 12:37:51,140e02c,NOJIRA - int2 dev for config deployer,Milan Černil,<EMAIL>,2025-03-20 07:11:01,,cid
v5.0.34,2025-07-24 12:37:51,040a09a,Merged in feature/NOJIRA/way4native-more-info-for-gde (pull request #966),Milan Černil,<EMAIL>,2025-03-20 07:11:01,feature/NOJIRA/way4native-more-info-for-gde,feature
v5.0.34,2025-07-24 12:37:51,2903a79,Merged in bugfix/*********-334/ipsThubRetryDelayFix (pull request #965),Nikolas Charalambidis,<EMAIL>,2025-03-19 16:46:37,bugfix/*********-334/ipsThubRetryDelayFix,bugfix
v5.0.34,2025-07-24 12:37:51,1b7232c,Merged in feature/*********-335/vbFinshapeApi1.4.6 (pull request #961),Nikolas Charalambidis,<EMAIL>,2025-03-19 08:58:32,feature/*********-335/vbFinshapeApi1.4.6,feature
v5.0.34,2025-07-24 12:37:51,0b4ebf5,CID: 4.1.21,Nikolas Charalambidis,<EMAIL>,2025-03-19 06:25:50,,cid
v5.0.34,2025-07-24 12:37:51,e091c25,Merged in bugfix/*********-7377/clientReplicationGdsFix2 (pull request #964),Nikolas Charalambidis,<EMAIL>,2025-03-19 06:25:47,bugfix/*********-7377/clientReplicationGdsFix2,bugfix
v5.0.34,2025-07-24 12:37:51,7de5e9d,CID: 4.1.20,Nikolas Charalambidis,<EMAIL>,2025-03-19 06:25:46,,cid
v5.0.34,2025-07-24 12:37:51,0b7ee2a,Merged in feature/*********-7239/clientReplicationGdsFix (pull request #962),Nikolas Charalambidis,<EMAIL>,2025-03-18 14:51:46,feature/*********-7239/clientReplicationGdsFix,feature
v5.0.34,2025-07-24 12:37:51,11c35fb,Merged in bugfix/*********-7378/addedProductStatusOnMissingProductIdFix (pull request #963),Nikolas Charalambidis,<EMAIL>,2025-03-18 14:51:04,bugfix/*********-7378/addedProductStatusOnMissingProductIdFix,feature
v5.0.34,2025-07-24 12:37:51,cdb99c2,Merged in feature/*********-7239/clientReplicationFnFieldsFix (pull request #960),Nikolas Charalambidis,<EMAIL>,2025-03-17 10:41:11,feature/*********-7239/clientReplicationFnFieldsFix,feature
v5.0.34,2025-07-24 12:37:51,0558cde,*********-7044 - fix dateFrom value in spx request,Jakub Jirsa,<EMAIL>,2025-03-17 10:34:22,,bugfix
v5.0.34,2025-07-24 12:37:51,49b9a7a,Merged in feature/*********-322/patchSelectionSerializer (pull request #959),Nikolas Charalambidis,<EMAIL>,2025-03-17 09:34:06,feature/*********-322/patchSelectionSerializer,feature
v5.0.34,2025-07-24 12:37:51,ca1076a,Merged in feature/*********-7348/HolderFiscalCode-missing (pull request #958),Milan Černil,<EMAIL>,2025-03-17 09:19:24,feature/*********-7348/HolderFiscalCode-missing,feature
v5.0.34,2025-07-24 12:37:51,6d22b2f,CID: 4.1.18,Nikolas Charalambidis,<EMAIL>,2025-03-17 05:08:29,,cid
v5.0.34,2025-07-24 12:37:51,1cfdab7,NOJIRA: TODO: *********-322,Nikolas Charalambidis,<EMAIL>,2025-03-14 11:08:07,,task
v5.0.34,2025-07-24 12:37:51,9d44872,"*********-257 - adjuct cards flows by visa P2P alias, payment credentials udpates",Jakub Jirsa,<EMAIL>,2025-03-13 19:04:20,,task
v5.0.34,2025-07-24 12:37:51,6816b59,Merged in feature/*********-318/consistencyCheck (pull request #955),Nikolas Charalambidis,<EMAIL>,2025-03-13 15:28:44,feature/*********-318/consistencyCheck,feature
v5.0.34,2025-07-24 12:37:51,289fdf0,Merged in fix/*********-7334/Issue-when-merchant-daily-statements-return-404 (pull request #954),Milan Černil,<EMAIL>,2025-03-13 13:04:54,fix/*********-7334/Issue-when-merchant-daily-statements-return-404,bugfix
v5.0.34,2025-07-24 12:37:51,e9366dd,Merged in feature/*********-251/createAliasE2E (pull request #943),Nikolas Charalambidis,<EMAIL>,2025-03-12 19:01:49,feature/*********-251/createAliasE2E,feature
v5.0.34,2025-07-24 12:37:51,a26664d,Merged in bugfix/*********-304/mockErrorHandling (pull request #948),Nikolas Charalambidis,<EMAIL>,2025-03-12 15:40:43,bugfix/*********-304/mockErrorHandling,bugfix
v5.0.34,2025-07-24 12:37:51,e8d9c49,Merged in feature/*********-7239/clientReplicationFix (pull request #953),Nikolas Charalambidis,<EMAIL>,2025-03-12 15:40:08,feature/*********-7239/clientReplicationFix,feature
v5.0.34,2025-07-24 12:37:51,4531e07,Merged in feature/*********-7239/clientReplication (pull request #941),Nikolas Charalambidis,<EMAIL>,2025-03-12 11:56:41,feature/*********-7239/clientReplication,feature
v5.0.34,2025-07-24 12:37:51,2d97e91,Merged in fix/typo (pull request #951),Tomáš Lipovský,<EMAIL>,2025-03-11 10:29:23,fix/typo,bugfix
v5.0.34,2025-07-24 12:37:51,476b9c6,Merged in fix/cid/cid-2-compatibility (pull request #949),Roman Knotek,<EMAIL>,2025-03-11 10:25:56,fix/cid/cid-2-compatibility,bugfix
v5.0.34,2025-07-24 12:37:51,771e446,Merged in bugfix/*********-304/mockErrorHandling (pull request #946),Nikolas Charalambidis,<EMAIL>,2025-03-10 10:12:19,bugfix/*********-304/mockErrorHandling,bugfix
v5.0.34,2025-07-24 12:37:51,eae9a0b,Merged in feature/*********-7175/rabbitMqDeadLettering (pull request #940),Nikolas Charalambidis,<EMAIL>,2025-03-08 17:23:08,feature/*********-7175/rabbitMqDeadLettering,feature
v5.0.34,2025-07-24 12:37:51,e017d86,"*********-252 - create card p2p alias, adjust card mask enricher",Jakub Jirsa,<EMAIL>,2025-03-07 22:15:55,,task
v5.0.34,2025-07-24 12:37:51,9a2c3d8,Merged in feature/*********-7122/Treasury-IBAN (pull request #944),Milan Černil,<EMAIL>,2025-03-07 11:51:42,feature/*********-7122/Treasury-IBAN,feature
v5.0.34,2025-07-24 12:37:51,eb821ea,Merged in feat/vb-ldap (pull request #942),Tomáš Lipovský,<EMAIL>,2025-03-07 10:28:29,feat/vb-ldap,feature
v5.0.34,2025-07-24 12:37:51,a7cb587,NOJIRA - app auth url for 3ds adjusted,Milan Černil,<EMAIL>,2025-03-05 12:43:06,,task
v5.0.34,2025-07-24 12:37:51,3a55d94,NOJIRA - fix GAAS liquibase,Milan Černil,<EMAIL>,2025-03-05 10:49:13,,bugfix
v5.0.34,2025-07-24 12:37:51,5b1e177,NOJIRA - fix GDE deployment,Milan Černil,<EMAIL>,2025-03-05 08:52:21,,bugfix
v5.0.34,2025-07-24 12:37:51,f703a9d,Version 5.0.3-SNAPSHOT,quickbuild,<EMAIL>,2025-03-04 14:54:54,,task
v5.0.34,2025-07-24 12:37:51,25b972b,Version 5.0.2,quickbuild,<EMAIL>,2025-03-04 14:41:46,,task
v5.0.34,2025-07-24 12:37:51,48d5c28,Merged in feature/*********-251/visaAdsMock2 (pull request #936),Nikolas Charalambidis,<EMAIL>,2025-03-04 14:11:40,feature/*********-251/visaAdsMock2,feature
v5.0.34,2025-07-24 12:37:51,3de5e80,NOJIRA: Merge from release/4.1,Jakub Jirsa,<EMAIL>,2025-03-04 14:10:31,,task
v5.0.34,2025-07-24 12:37:51,e219578,Merged in feature/*********-259/visaAdsSecurityFix (pull request #937),Nikolas Charalambidis,<EMAIL>,2025-03-04 13:54:18,feature/*********-259/visaAdsSecurityFix,feature
v5.0.34,2025-07-24 12:37:51,e53ac0f,Merged in feature/*********-290/5.0.1-fixes (pull request #933),Nikolas Charalambidis,<EMAIL>,2025-03-04 13:53:58,feature/*********-290/5.0.1-fixes,feature
v5.0.34,2025-07-24 12:37:51,2719597,Merged in feature/*********-198/spxBusinessApiFix (pull request #935),Nikolas Charalambidis,<EMAIL>,2025-03-04 09:17:55,feature/*********-198/spxBusinessApiFix,feature
v5.0.34,2025-07-24 12:37:51,f29aa3d,Version 5.0.2-SNAPSHOT,quickbuild,<EMAIL>,2025-03-03 14:56:41,,task
v5.0.34,2025-07-24 12:37:51,9dccf67,Version 5.0.1,quickbuild,<EMAIL>,2025-03-03 14:45:58,,task
v5.0.34,2025-07-24 12:37:51,75aa5ce,Merged in feature/*********-255/delete-visa-alias (pull request #931),Jakub Jirsa,<EMAIL>,2025-03-03 14:44:34,feature/*********-255/delete-visa-alias,feature
v5.0.34,2025-07-24 12:37:51,e9f9351,Merged in feature/*********-251/visaAdsMock (pull request #929),Nikolas Charalambidis,<EMAIL>,2025-03-03 11:06:36,feature/*********-251/visaAdsMock,feature
v5.0.34,2025-07-24 12:37:51,21250a7,Merged in fix/release4.1/*********-7173/new-card-request---illegal-currency (pull request #928) - fix yaml version,Milan Černil,<EMAIL>,2025-03-03 07:20:35,fix/release4.1/*********-7173/new-card-request---illegal-currency,feature
v5.0.34,2025-07-24 12:37:51,c569f0f,Merged in fix/release4.1/*********-7173/new-card-request---illegal-currency (pull request #928),Milan Černil,<EMAIL>,2025-03-03 07:14:11,fix/release4.1/*********-7173/new-card-request---illegal-currency,feature
v5.0.34,2025-07-24 12:37:51,d508f3f,Merged in feature/*********-251/createP2PAlias (pull request #923),Nikolas Charalambidis,<EMAIL>,2025-02-28 19:00:54,feature/*********-251/createP2PAlias,feature
v5.0.34,2025-07-24 12:37:51,ff0be14,Merged in bugfix/*********-274/5.0.0-fixes (pull request #927),Nikolas Charalambidis,<EMAIL>,2025-02-28 06:12:04,bugfix/*********-274/5.0.0-fixes,bugfix
v5.0.34,2025-07-24 12:37:51,472f2fc,Merge branch 'release/4.1' into develop,Jakub Jirsa,<EMAIL>,2025-02-27 12:38:34,,task
v5.0.34,2025-07-24 12:37:51,266d45e,Fix/*********-7173/add filtering pruduct catalog values by currency,Jakub Jirsa,<EMAIL>,2025-02-27 11:07:13,,feature
v5.0.34,2025-07-24 12:37:51,b2d183f,Merged in feature/*********-272/removeDeprecatedEndpoints (pull request #926),Nikolas Charalambidis,<EMAIL>,2025-02-27 08:45:05,feature/*********-272/removeDeprecatedEndpoints,feature
v5.0.34,2025-07-24 12:37:51,a7da025,Version 5.0.1-SNAPSHOT,quickbuild,<EMAIL>,2025-02-26 14:59:08,,task
v5.0.34,2025-07-24 12:37:51,7462ef4,Version 5.0.0,quickbuild,<EMAIL>,2025-02-26 14:48:36,,task
v5.0.34,2025-07-24 12:37:51,ba11d5d,Merged in feature/*********-198/createP2PAlias (pull request #885),Nikolas Charalambidis,<EMAIL>,2025-02-26 10:47:13,feature/*********-198/createP2PAlias,feature
v5.0.34,2025-07-24 12:37:51,a097878,NOJIRA - RabbitMqSystemIdentification for Branches route out operation,Milan Černil,<EMAIL>,2025-02-26 10:21:09,,task
v5.0.34,2025-07-24 12:37:51,0cb5a9c,Merged in feature/NOJIRA/5.0 (pull request #924),Nikolas Charalambidis,<EMAIL>,2025-02-26 10:03:32,feature/NOJIRA/5.0,feature
