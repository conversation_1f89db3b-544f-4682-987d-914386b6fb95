================================================================================
GIT RELEASE LOG
================================================================================

📋 TAG: v5.0.36
📅 Date: 2025-07-28 13:02:52
🎯 Target Commit: d11b0d7
📊 Commits: 4
----------------------------------------
  🔸 d11b0d7 - Version 5.0.36 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-28 13:02:52

  🔸 1e6615a - Merged in fix/5.0.35/fix-mle-private-key (pull request #1302) [fix/5.0.35/fix-mle-private-key] (bugfix)
     👤 <PERSON><PERSON> <<EMAIL>> on 2025-07-28 13:01:43

  🔸 9faade9 - Merged in feature/CID/5.0.35 (pull request #1301) [feature/CID/5.0.35] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-28 10:04:48

  🔸 5213a72 - Version 5.0.36-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-25 19:02:50


📋 TAG: v5.0.35
📅 Date: 2025-07-25 18:57:23
🎯 Target Commit: 8a8a4db
📊 Commits: 6
----------------------------------------
  🔸 8a8a4db - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 18:57:23

  🔸 42bbcce - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 18:55:02

  🔸 f56db08 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 15:02:14

  🔸 46a89b4 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-25 12:50:41

  🔸 3abd138 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 14:08:13

  🔸 806481d - Version 5.0.35-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-24 12:43:36


📋 TAG: v5.0.34
📅 Date: 2025-07-24 12:37:51
🎯 Target Commit: 2f8678c
📊 Commits: 481
----------------------------------------
  🔸 2f8678c - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 12:37:51

  🔸 9b74497 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 11:39:39

  🔸 318a16a - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 11:02:55

  🔸 8766de8 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 11:02:55

  🔸 9797305 - Unable to read commit message (task)
     👤 Unknown <<EMAIL>> on 2025-07-24 09:52:01

  🔸 31d85a1 - *********-435 - remove mapping for inapp visa google registration (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-23 21:43:08
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-23 21:43:08

  🔸 a92bb8c - *********-7269 - remove formating dates depend on statement type for retail clients (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-23 21:43:07
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-23 21:43:07

  🔸 5c0f2f9 - NOJIRA: ExternalSystemIntegration documentation (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-23 11:15:52

  🔸 c9bff7b - *********-417 - add option to get card provisioning data by card client and expiration date (only Google Mastercard) (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-22 20:08:43

  🔸 a54bb68 - *********-9084: Friends and Family - allow processing all clients (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-22 13:57:56

  🔸 e127f39 - fix build III (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-22 13:03:39

  🔸 8dad8d9 - fix build II (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-22 12:45:42

  🔸 156d06f - cherrypick - Merged in feature/*********-9051/request/response-logging-log4j (pull request #1273) *********-9051 Review request/response logging (toString) [feature/*********-9051/request/response-logging-log4j] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-07-22 12:33:07

  🔸 10ddb05 - NOJIRA - build fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-22 12:30:27

  🔸 32ef6f5 - *********-9075 - add generating rrn depend on source component, remap target account for credit IPS payment (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-21 12:41:02
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-21 12:41:02

  🔸 869cebe - Merged in feature/*********-50/upsThub (pull request #1287) [feature/*********-50/upsThub] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-21 10:25:09

  🔸 016d2ac - CID 5.0.33 - INT2,DEV,UAT2 (cid)
     👤 Milan Černil <<EMAIL>> on 2025-07-18 15:55:15

  🔸 8f5ea5e - CID 4.3.12 - INT,TEST,UAT (cid)
     👤 Milan Černil <<EMAIL>> on 2025-07-18 14:44:38

  🔸 3560f0e - Version 5.0.34-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-18 14:19:15

  🔸 808b5fd - Version 5.0.33 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-18 14:13:44

  🔸 7fc4383 - Merged in task/NOJIRA/CID.md (pull request #1280) [task/NOJIRA/CID.md] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-18 13:50:17

  🔸 283a094 - Merged in fix/*********-7269/use-create-at (pull request #1284) [fix/*********-7269/use-create-at] (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-18 13:37:43
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-07-18 13:37:43

  🔸 a37df46 - Merged in feature/4.3/*********-8982/Add-PIN-for-new-card (pull request #1286) [feature/4.3/*********-8982/Add-PIN-for-new-card] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-07-18 13:29:14

  🔸 d552e76 - Merged in feat/cms-ln-pwd-support-variable (pull request #1285) [feat/cms-ln-pwd-support-variable] (feature)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-07-18 11:30:11

  🔸 0b9a264 - Merged in bugfix/*********-9027/gdeExceptionWithRouteOutMessageData (pull request #1278) [bugfix/*********-9027/gdeExceptionWithRouteOutMessageData] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-18 09:41:49

  🔸 817533d - Merged in bugfix/*********-8982/improvement (pull request #1258) [bugfix/*********-8982/improvement] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-18 09:41:39

  🔸 dee117f - Merged in fix/ln-pwd-prod (pull request #1281) [fix/ln-pwd-prod] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-07-17 16:48:57

  🔸 e9e3b32 - Merged in feat/cms-redirect (pull request #1276) [feat/cms-redirect] (feature)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-07-17 13:12:49

  🔸 b7caade - Merged in feature/*********-50/head (pull request #1279) [feature/*********-50/head] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-17 09:05:06

  🔸 4dd76f3 - *********-50: Subscription management - TPM-GDE-UPS (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-15 21:31:07

  🔸 1964f4c - Merged in fix/4.3/*********-8859/null-sum (pull request #1277) [fix/4.3/*********-8859/null-sum] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-15 16:09:35

  🔸 83ee9e7 - Merged in fix/redirect-uri-2 (pull request #1274) [fix/redirect-uri-2] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-07-15 14:39:15

  🔸 38cabed - CID: CMS update env variables (task)
     👤 Jan Dockal <<EMAIL>> on 2025-07-15 13:52:52

  🔸 d1e6999 - Document upload fix + 3D secure app link fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-15 11:08:33

  🔸 0b4669f - *********-7269 - fix statement date format (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-15 10:06:55
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-15 10:06:55

  🔸 14fd9a9 - Merged in feature/*********-50/e2eFix (pull request #1268) [feature/*********-50/e2eFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-14 13:41:14

  🔸 6ae4f5b - Merged in bugfix/*********-8640/gaas16.1.0-M.6 (pull request #1272) [bugfix/*********-8640/gaas16.1.0-M.6] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-14 13:21:12

  🔸 f4b3b19 - Merged in bugfix/*********-910/improvement (pull request #1271) [bugfix/*********-910/improvement] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-14 09:44:13

  🔸 77d7255 - NOJIRA: Fixes e2e-test.properties (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-10 12:12:25

  🔸 a116dfb - Version 5.0.33-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-10 12:11:20

  🔸 0eb5a9c - Version 5.0.32 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-10 12:05:44

  🔸 6c50198 - Merged in task/*********-915/5.0.31-fixes (pull request #1267) [task/*********-915/5.0.31-fixes] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-10 11:59:05

  🔸 e716e40 - *********-7832: fix loading last updated date from statement API, fix typo in template, fix stretch for remittance information (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-10 09:48:38
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-10 09:48:38

  🔸 9c2d215 - Merged in task/*********-915/5.0.31-fixes (pull request #1263) [task/*********-915/5.0.31-fixes] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-10 09:06:25

  🔸 3d72eab - Merged in feature/*********-778-Component-UPS-CID (pull request #1264) [feature/*********-778-Component-UPS-CID] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-09 21:49:27

  🔸 c459cf5 - Merged in feature/*********-50/ups-payment-table (pull request #1261) [feature/*********-50/ups-payment-table] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-08 11:47:55

  🔸 5372c49 - Version 5.0.32-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-08 11:24:25

  🔸 99283d9 - Version 5.0.31 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-08 11:18:54

  🔸 adcfaad - Merged in feature/*********-50/ups (pull request #1204) [feature/*********-50/ups] (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-08 11:08:26
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-07-08 11:08:26

  🔸 fd45526 - *********-7068 - fix floating frame position in retail template (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:40

  🔸 8a0ccce - Fix/*********-7068/fix retail template (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:38
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:38

  🔸 81b7e08 - *********-8354 - fix print to TXT and XML (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:35
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:35

  🔸 df94a7f - Merge remote-tracking branch 'origin/develop' into develop (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:42:15

  🔸 c3025cc - *********-8830 - change pdf creator to "B.C. VICTORIABANK S.A." and producer to "Finshape Global Print Server", merge with develop (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:41:51

  🔸 ffb2f44 - *********-8830 - change pdf creator to "B.C. VICTORIABANK S.A." and producer to "Finshape Global Print Server" (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-07-04 11:37:07

  🔸 a13dcfb - Merged in fix/4.3/*********-8979/29-Lack-of-User-Input-Validation-Length (pull request #1255) [fix/4.3/*********-8979/29-Lack-of-User-Input-Validation-Length] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-04 10:50:26

  🔸 95e542d - Version 5.0.31-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-03 16:31:45

  🔸 e02b0fe - Version 5.0.30 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-03 16:26:16

  🔸 382121a - fix test (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-03 16:17:34

  🔸 f443832 - Build fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-07-03 14:58:20

  🔸 23af2e1 - CID: CMS bump version (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-07-03 14:20:07

  🔸 a26c9ff - Merged in feature/4.3/*********-8859/SPX/documents-upload-throttling (pull request #1249) *********-8859 [Documents] BE SPX - Define and Implement File Upload Limits to Prevent System Overload [feature/4.3/*********-8859/SPX/documents-upload-throttling] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-07-03 13:00:16

  🔸 fcb8a5c - CID: WEB change versioning of monorepo (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-07-03 10:20:50

  🔸 5f0b251 - CID: 5.0.29 (VB INT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 18:48:20

  🔸 b01f487 - CID: 4.3.7 (Finshape TEST, VB INT) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 17:47:43

  🔸 504f16f - Version 5.0.30-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-07-02 17:10:11

  🔸 f6e1472 - Version 5.0.29 (task)
     👤 quickbuild <<EMAIL>> on 2025-07-02 17:04:42

  🔸 ca244a7 - Merged in bugfix/*********-677/aliasProfileNameFix (pull request #1250) [bugfix/*********-677/aliasProfileNameFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 16:25:35

  🔸 cdf5ea3 - Merged in feature/*********-697/swiftCatalogsDeadLettering2 (pull request #1246) [feature/*********-697/swiftCatalogsDeadLettering2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 15:14:01

  🔸 b9f9492 - Merged in bugfix/nojira/p2pPaymentOrderE2ETestFix (pull request #1247) [bugfix/nojira/p2pPaymentOrderE2ETestFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 15:13:54

  🔸 452ecdb - Merged in feature/*********-8858/maxAttachmentCountValidator (pull request #1245) [feature/*********-8858/maxAttachmentCountValidator] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-02 12:34:09

  🔸 a082dd9 - CID: WAC bump version (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-07-01 15:53:42

  🔸 083229a - Merged in feature/*********-8694/gaapiDoSProtection2 (pull request #1244) [feature/*********-8694/gaapiDoSProtection2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-07-01 12:11:23

  🔸 66184c5 - Merged in fix/max-sessions-develop (pull request #1218) [fix/max-sessions-develop] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-07-01 11:13:04
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-07-01 11:13:04

  🔸 76758e5 - *********-8614: More Friends & Family (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-30 14:35:37

  🔸 b407329 - Merged in fix/4.3/*********-492/Movements-new-gateway (pull request #1237) *********-492 new gateway address, cid adjusted [fix/4.3/*********-492/Movements-new-gateway] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-06-30 07:55:05

  🔸 95b6fef - Merged in fix/NOJIRA/k8s-config (pull request #1238) [fix/NOJIRA/k8s-config] (bugfix)
     👤 Jan Holec <<EMAIL>> on 2025-06-27 22:02:20
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-06-27 22:02:20

  🔸 ae42d1c - CID: 5.0.28 (VB INT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-27 15:14:47

  🔸 5b02ec4 - Version 5.0.29-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-06-27 14:02:46

  🔸 1b850c9 - Version 5.0.28 (task)
     👤 quickbuild <<EMAIL>> on 2025-06-27 13:57:12

  🔸 880cf25 - CID: 4.3.6 (Finshape TEST, VB INT) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-27 13:52:05

  🔸 446e14a - Merged in bugfix/*********-555/rrnMappingForP2PFix (pull request #1232) [bugfix/*********-555/rrnMappingForP2PFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-27 13:48:45

  🔸 08256a3 - *********-7269 - anonymize data for IPS prints (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-27 11:16:50
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-27 11:16:50

  🔸 ab3a868 - CID: CMS upgrade migration scripts (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-06-27 10:39:53

  🔸 7033374 - Merged in bugfix/4.3/*********-8959 (pull request #1234) [bugfix/4.3/*********-8959] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-27 10:32:16

  🔸 4e3983f - Remove E2E Test annotation (task)
     👤 Milan Černil <<EMAIL>> on 2025-06-27 10:14:35

  🔸 799a66d - Merged in feature/1b/*********-8893/pin-and-e2e (pull request #1236) [feature/1b/*********-8893/pin-and-e2e] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-06-27 10:01:31

  🔸 551f876 - *********-8916 Add Activate card to Digital Retail Card Creation - e2e test (feature)
     👤 Milan Černil <<EMAIL>> on 2025-06-26 15:26:52

  🔸 29b6601 - Merged in feature/4.3/*********-8916/Add-Activate-card-to-Digital-Retail-Card-Creation (pull request #1231) [feature/4.3/*********-8916/Add-Activate-card-to-Digital-Retail-Card-Creation] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-06-26 10:57:40

  🔸 e6f3a91 - CID: CMS upgrade migration scripts (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-06-26 09:33:38

  🔸 be7d6c7 - Merged in feature/*********-691/swiftCatalogMappings (pull request #1203) [feature/*********-691/swiftCatalogMappings] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-24 11:26:46

  🔸 f7ef4b7 - Merged in bugfix/*********-8932/displayIdFix (pull request #1229) [bugfix/*********-8932/displayIdFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-24 11:24:59

  🔸 897b229 - *********-23 - bruno collection for inapp (hswm wallet) (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-24 10:14:02
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-24 10:14:02

  🔸 0af02cb - Merged in bugfix/*********-8918/cardGeneratorFix (pull request #1225) [bugfix/*********-8918/cardGeneratorFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-23 15:40:45

  🔸 883bef0 - Merged in feature/*********-8337/friendsAndFamily6 (pull request #1220) [feature/*********-8337/friendsAndFamily6] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-23 15:40:16

  🔸 d15be7a - Merged in feature/*********-697/swiftCatalogsDeadLettering (pull request #1202) [feature/*********-697/swiftCatalogsDeadLettering] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-23 09:22:25

  🔸 bed02ca - Merged in feat/deploy-prod-yaml (pull request #1215) [feat/deploy-prod-yaml] (feature)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-06-20 16:05:05

  🔸 e7491ea - set variable values for session parameters (task)
     👤 Tomas Lipovsky <<EMAIL>> on 2025-06-20 16:05:05
     🔧 Committed by Tomáš Lipovský <<EMAIL>> on 2025-06-20 16:05:05

  🔸 257cc05 - E2E - fix Treasury (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-20 11:22:15

  🔸 8fe8fb5 - *********-8888 - fix changing RTP status to accepted after pay, add scheduled job which delete RTPs older than 30d (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-20 10:31:19
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-06-20 10:31:19

  🔸 7bfac41 - Merged in feature/*********-8337/friendsAndFamily5 (pull request #1212) [feature/*********-8337/friendsAndFamily5] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-20 10:13:58

  🔸 61cced3 - Merged in fix/E2E (pull request #1214) [fix/E2E] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-19 16:20:30

  🔸 2bb0d88 - Merged in fix/4.3/*********-7675/Liquibase-master-changelog-issues (pull request #1211) [fix/4.3/*********-7675/Liquibase-master-changelog-issues] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-19 09:58:16

  🔸 4ad1f73 - E2E tests fix II (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-19 08:03:43

  🔸 c53e905 - E2E tests fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-19 07:42:34

  🔸 e8dc096 - CID: 4.3.2 (Finshape TEST) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-18 15:16:36

  🔸 cd9be55 - Merged in feature/4.3/movementRrnStored (pull request #1208) [feature/4.3/movementRrnStored] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-06-18 13:41:34
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-06-18 13:41:34

  🔸 5dd7cd2 - Merged in feature/*********-8874/friendsAndFamilyDummyMode (pull request #1205) [feature/*********-8874/friendsAndFamilyDummyMode] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-18 13:40:52

  🔸 42bb2b3 - Merged in feature/*********-8337/friendsAndFamily4 (pull request #1206) [feature/*********-8337/friendsAndFamily4] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-18 13:24:14

  🔸 1c9e27b - Merged in bugfix/*********-8873/xcrLimitsReplicationFix (pull request #1207) [bugfix/*********-8873/xcrLimitsReplicationFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-18 13:24:14

  🔸 b199c31 - Merged in bugfix/*********-7716/instructionStatusTransitionListenerImprovements (pull request #1201) [bugfix/*********-7716/instructionStatusTransitionListenerImprovements] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-18 09:19:42

  🔸 782cd6c - Merged in feature/gaas-update-cherry (pull request #1199) [feature/gaas-update-cherry] (task)
     👤 Milan Černil <<EMAIL>> on 2025-06-18 07:47:43

  🔸 bbc1026 - Merged in fix/4.2/deployer-shell-fixes (pull request #1183) [fix/4.2/deployer-shell-fixes] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-17 08:12:49

  🔸 261b47b - CID: 4.3.1 (Finshape TEST) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-16 10:32:31

  🔸 17259af - *********-8830 - change pdf creator to "B.C. VICTORIABANK S.A." from Jasper 6.21.2 (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-16 09:58:15
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-16 09:58:15

  🔸 2ba1c71 - Merged in feature/*********-681/swiftCatalogUpdate (pull request #1198) [feature/*********-681/swiftCatalogUpdate] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-16 09:51:12

  🔸 fe7fe10 - Merged in feature/*********-8337/friendsAndFamily3 (pull request #1200) [feature/*********-8337/friendsAndFamily3] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-16 09:30:30

  🔸 7f19539 - CID: 4.2.25 (VB INT) (cid)
     👤 Milan Černil <<EMAIL>> on 2025-06-12 09:12:44

  🔸 e2dec5b - CID INT, INT2, UAT, UAT2 GAAS dummy user ************* (cid)
     👤 Milan Černil <<EMAIL>> on 2025-06-12 09:11:31

  🔸 ae8bf71 - Merged in feature/*********-8337/friendsAndFamily2 (pull request #1196) [feature/*********-8337/friendsAndFamily2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-12 08:51:57

  🔸 05d0f12 - *********-8801 - add enriching residency into IPS*instruction (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-11 21:43:28

  🔸 55f5f75 - CID: 4.2.25 (Finshape TEST, VB UAT) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-11 18:15:58

  🔸 52c1744 - Merged in feature/*********-8628/redirectUrlFix (pull request #1195) [feature/*********-8628/redirectUrlFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-11 18:14:32

  🔸 3f91a45 - Merged in bugfix/*********-8791/npeFixInValidators (pull request #1192) [bugfix/*********-8791/npeFixInValidators] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-11 15:05:25

  🔸 dd03ce8 - *********-8795 - fix loading data from GDS for POS report (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-11 14:12:29
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-11 14:12:29

  🔸 a3efc16 - fixup! *********-8153 - rename property virtual-host to virtualHost (bugfix)
     👤 Jan Dockal <<EMAIL>> on 2025-06-11 13:05:18

  🔸 c4d5cad - *********-8153 - rename property virtual-host to virtualHost (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-11 12:16:45

  🔸 10749fc - *********-7814 - fix locale properties for prints, add double spaces for statement list, adjust bru collection (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-11 12:09:10
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-11 12:09:10

  🔸 61832b7 - CID: CMS - bump migrations version (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-06-11 12:06:09

  🔸 2d081e9 - CID: CMS - bump migrations version (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-06-11 11:54:16

  🔸 05feb6a - *********-8694: GAAPI protection against DDoS using rate limiter (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-11 11:13:52

  🔸 c51406e - Merged in feature/*********-15/resolveAliasProfileNullHandling (pull request #1188) [feature/*********-15/resolveAliasProfileNullHandling] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-11 09:42:29

  🔸 0f72b60 - Merged in fix/env (pull request #1181) [fix/env] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-06-10 11:34:30

  🔸 4717b25 - Merged in fix/4.2/*********-8793/spx-file-name-sanitation (pull request #1184) [fix/4.2/*********-8793/spx-file-name-sanitation] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-10 09:55:58
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-06-10 09:55:58

  🔸 69f1994 - Merged in feature/*********-370/componentUpdates (pull request #1008) [feature/*********-370/componentUpdates] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-09 10:25:03

  🔸 46ec1ad - *********-8053 - NoTomorrowValidator - attribute validator for input date (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-09 10:13:13

  🔸 b719a58 - Merged in feature/*********-8337/friendsAndFamilyFixes (pull request #1179) [feature/*********-8337/friendsAndFamilyFixes] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-09 09:52:35

  🔸 9615a69 - Merged in bugfix/*********-8785/mdFiscalCodeValidator (pull request #1178) [bugfix/*********-8785/mdFiscalCodeValidator] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-09 09:35:50

  🔸 419ee88 - Merged in feature/NOJIRA/configDeployerWatchFilePattern (pull request #1182) [feature/NOJIRA/configDeployerWatchFilePattern] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-09 09:07:52

  🔸 0b49b88 - Merged in feat/*********-8687-gaas-env-var (pull request #1164) [feat/*********-8687-gaas-env-var] (feature)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-06-06 16:56:01

  🔸 817764a - *********-7632 - fix dateFrom and dateTo for POS statements report, add timezoneOffsetInMinutes into print req (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-06 15:45:46
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-06 15:45:46

  🔸 8fc306d - *********-8781 - fix cid for GPS (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-06 15:45:45
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-06 15:45:45

  🔸 9be938a - Merged in feature/*********-8337/friendsAndFamilyAdjustments (pull request #1176) [feature/*********-8337/friendsAndFamilyAdjustments] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-06 15:38:02

  🔸 e80d04e - Merged in feature/*********-8337/friendsAndFamilyDevelop (pull request #1174) [feature/*********-8337/friendsAndFamilyDevelop] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-06 13:16:18

  🔸 64e1622 - CID: 4.2.22 (VB INT, VB UAT) + syntax fixes (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-06 11:33:41

  🔸 c3c571b - CID: ib-web set GPX_PUBLIC_URL (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-06-06 08:44:37

  🔸 54067ed - CID: 4.2.22 (Finshape TEST) + merge fixes (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 15:10:48

  🔸 2340a09 - NOJIRA: Merge fixes (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 13:39:17

  🔸 ea5f9f8 - Merged in feature/*********-8765/addPhysicalCardToDigitalProductEligibilityValidatorRenamed (pull request #1173) [feature/*********-8765/addPhysicalCardToDigitalProductEligibilityValidatorRenamed] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 13:30:02

  🔸 7d773a6 - Merged in bugfix/*********-7434/documentNumberAsString (pull request #1172) [bugfix/*********-7434/documentNumberAsString] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 13:29:22

  🔸 475caaa - Merged in feature/*********-8337/friendsAndFamily (pull request #1118) [feature/*********-8337/friendsAndFamily] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 13:29:16

  🔸 0225d03 - NOJIRA: Compilation fix (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-05 13:29:00

  🔸 08ab91c - *********-8686 - Sanitize input string values to prevent Excel formula injection (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-05 11:24:45
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-05 11:24:45

  🔸 32f6a50 - *********-7069 / remove redundant fileds (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-05 11:24:44
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-05 11:24:44

  🔸 7061e42 - *********-23 - rework inapp for Google (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-05 11:24:43

  🔸 705231b - *********-8667/upgrade gps sider, fix CVE-2025-48734 and CVE-2025-31672 for GPS (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-06-05 11:23:40
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-06-05 11:23:40

  🔸 c8da546 - Version 5.0.28-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-06-04 19:11:46

  🔸 193b34a - Version 5.0.27 (task)
     👤 quickbuild <<EMAIL>> on 2025-06-04 19:06:11

  🔸 13d3bae - Merged in feature/*********-638/fix (pull request #1169) [feature/*********-638/fix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-04 18:57:41

  🔸 385b575 - CID: 5.0.26 (Finshape DEV, VB INT2, VB UAT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-04 14:26:49

  🔸 6816ade - Version 5.0.27-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-06-04 13:51:22

  🔸 a43dad6 - Version 5.0.26 (task)
     👤 quickbuild <<EMAIL>> on 2025-06-04 13:45:23

  🔸 f0d0273 - Merged in feature/*********-8676/e2eAdjustmentsForNewMandatoryFields (pull request #1167) [feature/*********-8676/e2eAdjustmentsForNewMandatoryFields] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-04 11:27:19

  🔸 31902ed - Merged in feature/*********-15/aliasInquiryBatching (pull request #1165) [feature/*********-15/aliasInquiryBatching] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-04 09:32:52

  🔸 b61b56d - Merged in feature/*********-638/adjustAddCardP2PAliasRequestProcessing-Flow (pull request #1163) [feature/*********-638/adjustAddCardP2PAliasRequestProcessing-Flow] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-03 20:09:20

  🔸 153d787 - Merged in feature/*********-8676/maskNumberEnriching (pull request #1161) [feature/*********-8676/maskNumberEnriching] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-03 09:32:50

  🔸 79596fd - Version 5.0.26-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-06-02 18:46:24

  🔸 b1820c1 - Version 5.0.25 (task)
     👤 quickbuild <<EMAIL>> on 2025-06-02 18:40:48

  🔸 8281208 - Merged in task/*********-8609/dryRunCherryPick (pull request #1160) [task/*********-8609/dryRunCherryPick] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-02 18:15:45

  🔸 748c3ab - Merged in task/*********-632/branchIdEnrichingFix (pull request #1159) [task/*********-632/branchIdEnrichingFix] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-06-02 17:32:03

  🔸 b38f9b2 - Version 5.0.25-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-06-01 21:34:42

  🔸 bf5f7d3 - Version 5.0.24 (task)
     👤 quickbuild <<EMAIL>> on 2025-06-01 21:28:31

  🔸 4614ff9 - Merged in fix/4.2/*********-7517-VB/Treasury-fields (pull request #1157) [fix/4.2/*********-7517-VB/Treasury-fields] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-01 21:14:17

  🔸 f260df5 - Merged in fix/4.2/*********-8613/Treasury-Remittance-overriden (pull request #1156) [fix/4.2/*********-8613/Treasury-Remittance-overriden] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-06-01 17:48:11

  🔸 04ab4e7 - Version 5.0.24-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-29 17:45:04

  🔸 20840e1 - Version 5.0.23 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-29 17:39:05

  🔸 cfff0cd - Tpm update - fix tests (task)
     👤 Milan Černil <<EMAIL>> on 2025-05-29 17:23:42

  🔸 de6de16 - *********-8553: TPM 6.8.1, fix enrichers (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-29 13:11:24
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-29 13:11:24

  🔸 5f94012 - *********-23 - rework inapp for iOs *********-23 - rework inapp for iOs, merge with develop (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-29 09:41:28

  🔸 914222e - CID: fix UAT tags (bugfix)
     👤 Jan Dockal <<EMAIL>> on 2025-05-28 11:37:33

  🔸 5fd441a - fixup! CID: feature cms migrations (feature)
     👤 Jan Dockal <<EMAIL>> on 2025-05-28 11:35:29

  🔸 475492f - Merged in bugfix/*********-8006/genCardMappingFix (pull request #1151) [bugfix/*********-8006/genCardMappingFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-28 10:21:14
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-28 10:21:14

  🔸 22c641a - *********-8561 Update GDS to 3.2.X - gas_appuser password 'a' set (task)
     👤 Milan Černil <<EMAIL>> on 2025-05-28 09:04:11

  🔸 26be797 - CID: 5.0.22 (Finshape DEV, VB INT2, VB UAT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-27 05:02:12

  🔸 af21019 - Version 5.0.23-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-27 04:35:34

  🔸 b8bc973 - Version 5.0.22 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-27 04:29:51

  🔸 501452b - Merged in feature/4.2/gds-update (pull request #1147) [feature/4.2/gds-update] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-27 04:25:33

  🔸 8ae9981 - Merged in feature/*********-8557/eligibilityChanges (pull request #1145) [feature/*********-8557/eligibilityChanges] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-27 04:25:32

  🔸 f7a5889 - Merged in bugfix/*********-483/p2pEnrichingFixPaymentFee (pull request #1149) [bugfix/*********-483/p2pEnrichingFixPaymentFee] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-26 15:50:22

  🔸 9fb20f4 - Merged in feature/*********-484/npeFixP2PFeeEnriching (pull request #1148) [feature/*********-484/npeFixP2PFeeEnriching] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-26 15:48:18

  🔸 69d173d - *********-8562: GEN 25.4.2 (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-26 12:46:46

  🔸 25f6a22 - Merged in feature4.2/deploy-business-config (pull request #1142) [feature4.2/deploy-business-config] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-05-26 07:41:07

  🔸 267309d - VISA properties - enabled proxy (task)
     👤 Milan Černil <<EMAIL>> on 2025-05-23 10:43:56

  🔸 fa3fba6 - Merged in fix/e2e (pull request #1137) [fix/e2e] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-23 10:30:00

  🔸 e658b09 - *********-8547 - fix loading data from RTP in enricher (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-23 10:22:15
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-23 10:22:15

  🔸 2733a18 - *********-7814 - fix statements fonts (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-23 10:22:15
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-23 10:22:15

  🔸 84729aa - Add custom java truststore to GAAS (feature)
     👤 Jan Holec <<EMAIL>> on 2025-05-22 12:46:33

  🔸 b3844fc - *********-8151 - add scheduled task, which deleted expired RtpRequests, fix refuse/cancel rtp (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-21 13:40:06
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-21 13:40:06

  🔸 688ded4 - TOOL-0000 fix cid configuration for IPS (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-21 13:40:06
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-21 13:40:06

  🔸 0c1795a - Merged in bugfix/*********-8507/happyHourXcrFix (pull request #1135) [bugfix/*********-8507/happyHourXcrFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-21 09:26:19

  🔸 190f423 - Merged in bugfix/*********-8062/59BeneficiaryCustomer (pull request #1133) [bugfix/*********-8062/59BeneficiaryCustomer] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-21 04:16:53

  🔸 e11e3c1 - Merged in feature/*********-8304/mockLinks2 (pull request #1134) [feature/*********-8304/mockLinks2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-21 04:16:43

  🔸 e74170a - CID: 5.0.21 (Finshape DEV, VB INT2, VB UAT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-20 14:57:23

  🔸 34a700c - Version 5.0.22-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-20 13:28:46

  🔸 cabd374 - Version 5.0.21 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-20 13:22:41

  🔸 8475118 - Merged in bugfix/*********-15/codeAlpha3Fix (pull request #1132) [bugfix/*********-15/codeAlpha3Fix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-20 13:06:56

  🔸 4644f13 - Merged in e2e-test-fix (pull request #1131) [e2e-test-fix] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-20 11:49:32

  🔸 f929e1b - Merged in feature/NOJIRA/vb-ca-certificate (pull request #1130) [feature/NOJIRA/vb-ca-certificate] (feature)
     👤 Jan Holec <<EMAIL>> on 2025-05-20 11:00:38
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-05-20 11:00:38

  🔸 fa6dda3 - *********-8353: Adds lastMovementDate (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-20 10:19:44
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-20 10:19:44

  🔸 60754eb - *********-8303: Fixes RabbitMQ connection on Finshape environments (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 23:49:38
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-19 23:49:38

  🔸 76fa07d - Merged in bugfix/*********-7943/retailEurLimitFix (pull request #1128) [bugfix/*********-7943/retailEurLimitFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 17:58:21

  🔸 b933803 - Merged in bugfix/*********-8133/shortenName (pull request #1126) [bugfix/*********-8133/shortenName] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 17:58:20

  🔸 bffd764 - Merged in fix/4.2/*********-8474/freeze-unfreeze-card (pull request #1127) [fix/4.2/*********-8474/freeze-unfreeze-card] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-19 16:46:44

  🔸 44549e0 - Merged in bugfix/*********-15/ownerEntityAttributeMatchValidatorFix (pull request #1129) [bugfix/*********-15/ownerEntityAttributeMatchValidatorFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 16:36:17

  🔸 1872402 - Version 5.0.21-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-19 07:17:01

  🔸 d72a807 - Version 5.0.20 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-19 07:11:38

  🔸 d38e85e - Merged in feature/*********-8471/pushingFlag (pull request #1124) [feature/*********-8471/pushingFlag] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 06:20:24

  🔸 4670253 - Merged in feature/*********-15/visaPhoneNumberFix (pull request #1125) [feature/*********-15/visaPhoneNumberFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-19 06:15:05

  🔸 5887541 - Merged in feature/*********-15/codeAlpha3 (pull request #1122) [feature/*********-15/codeAlpha3] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-16 12:48:36

  🔸 160e997 - CID: CMS latest migrations (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-05-16 10:30:22

  🔸 ed7ccee - Merged in cid-apple-app-site-association (pull request #1026) [cid-apple-app-site-association] (cid)
     👤 Jan Dočkal <<EMAIL>> on 2025-05-16 09:54:48

  🔸 f265e11 - CID: Fix versions of neo ib-web a login (bugfix)
     👤 Jan Dockal <<EMAIL>> on 2025-05-15 22:52:17

  🔸 ceaac5e - Merged in bugfix/*********-7282/mockFixDevelop (pull request #1121) [bugfix/*********-7282/mockFixDevelop] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-15 15:50:07

  🔸 ac2c0f6 - Merged in fix/4.2/*********-8444/new-card-request-error (pull request #1116) [fix/4.2/*********-8444/new-card-request-error] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-05-15 15:03:38

  🔸 32b6b26 - Merged in fix/cid/terraform-cloud-config (pull request #1119) [fix/cid/terraform-cloud-config] (bugfix)
     👤 Roman Knotek <<EMAIL>> on 2025-05-15 14:34:54

  🔸 d387dfa - Merged in feature/*********-8187/isHappyHourFlag (pull request #1115) [feature/*********-8187/isHappyHourFlag] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-15 11:50:08

  🔸 241e9c4 - Business Configuration - fix output and input folders (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-15 11:27:04

  🔸 c6b8bdc - CID: 5.0.19 (VB INT2, VB UAT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-15 07:13:43

  🔸 26445ca - Version 5.0.20-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-14 13:23:29

  🔸 fe9a030 - Version 5.0.19 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-14 13:17:39

  🔸 696d01b - Merged in feature/*********-393/visaEntityId (pull request #1114) [feature/*********-393/visaEntityId] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-14 13:09:45

  🔸 347a22b - Merged in fix/4.2/*********-7877/account-detail (pull request #1113) [fix/4.2/*********-7877/account-detail] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-14 11:13:03

  🔸 ba9307f - *********-8302 - fix remittance information for type1 print (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-14 10:49:51
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-14 10:49:51

  🔸 f3e989f - Merged in feature/*********-15/visaBrunoCollections (pull request #1112) [feature/*********-15/visaBrunoCollections] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-14 08:35:45

  🔸 80df30a - Merged in feature/*********-15/visaBodyEncryptionFix (pull request #1111) [feature/*********-15/visaBodyEncryptionFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-14 07:19:16

  🔸 aa59773 - Merged in bugfix/*********-7641/thub1.0.0 (pull request #1110) [bugfix/*********-7641/thub1.0.0] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-14 07:18:21

  🔸 be8463c - Merged in feature/*********-15/maskVisaHeaders (pull request #1107) [feature/*********-15/maskVisaHeaders] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-13 12:40:07

  🔸 d785285 - rychla oprava duplicity v overrides (task)
     👤 jan_manina <<EMAIL>> on 2025-05-13 11:06:27
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-05-13 11:06:27

  🔸 5d73af6 - CID: 5.0.18 (VB INT2, VB UAT2) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-13 10:49:12

  🔸 1c051b0 - Version 5.0.19-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-13 10:44:25

  🔸 019ac0e - Version 5.0.18 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-13 10:38:55

  🔸 0b83700 - *********-7900 - add filling fee into paymentApi, cherry-pick from 4.2 and resolve conflicts (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-13 10:35:54

  🔸 56be3e4 - Merged in feature/*********-15/visaP2PAliasCidFix (pull request #1106) [feature/*********-15/visaP2PAliasCidFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-13 10:33:00

  🔸 ff74cc2 - Merged in feature/*********-15/phoneNumberFormatted (pull request #1104) [feature/*********-15/phoneNumberFormatted] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-13 06:23:40

  🔸 940a773 - *********-7993: Updates BSC Commons to 7.6.0 for custom BE components - E2E fix (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-12 16:02:07

  🔸 2fd4a0a - Version 5.0.18-SNAPSHOT (substituting partial failure of #1560 Jenkins build of develop branch) (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-12 15:55:33

  🔸 2919fa3 - *********-7011 - fix mapping for sender bank country (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-12 15:28:31

  🔸 cd2fc74 - Merged in feature/*********-15/aliasInquiryFixes (pull request #1103) [feature/*********-15/aliasInquiryFixes] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-12 15:17:33

  🔸 106dd93 - Merged in fix/4.2/*********-8415/GDE-message-processing-reconfiguration (pull request #1097) [fix/4.2/*********-8415/GDE-message-processing-reconfiguration] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-05-12 14:28:49

  🔸 24cf3ce - Merged in bugfix/*********-7939/happyHourTotalAmountFix (pull request #1099) [bugfix/*********-7939/happyHourTotalAmountFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-12 11:03:37

  🔸 6343bc3 - Merged in feature/*********-7993/bscCommonsCustomComponents7.60 (pull request #1102) [feature/*********-7993/bscCommonsCustomComponents7.60] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-12 11:03:27

  🔸 cc44316 - Merged in feature/filebeat-ssl_console-logging-level (pull request #1091) [feature/filebeat-ssl_console-logging-level] (feature)
     👤 Ján Manina <<EMAIL>> on 2025-05-12 10:47:57
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-12 10:47:57

  🔸 808d1a5 - NOJIRA-release 4.2.15 (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-06 15:43:52

  🔸 9eeeadb - Version 5.0.17-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-06 15:30:00

  🔸 037d877 - Version 5.0.16 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-06 15:23:25

  🔸 a84e031 - *********-23 In app provisioning (Apple, Google) - BE (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-06 13:44:33

  🔸 7fa2fe9 - Merged in feature/*********-8280/CARDS-display-of-currency-for-additional-cardholder (pull request #1094) [feature/*********-8280/CARDS-display-of-currency-for-additional-cardholder] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-05-06 13:35:08

  🔸 0c69211 - CID: web - lock 1B component versions (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-05-05 14:59:54

  🔸 caa5e72 - Merged in fix/*********-7282/cid-rabbitmq (pull request #1032) [fix/*********-7282/cid-rabbitmq] (bugfix)
     👤 Jan Holec <<EMAIL>> on 2025-05-05 13:28:30
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-05 13:28:30

  🔸 b9a0848 - Merged in feature/NOJIRA/cid-gaas-fix (pull request #1090) [feature/NOJIRA/cid-gaas-fix] (feature)
     👤 Jan Holec <<EMAIL>> on 2025-05-05 12:43:35
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-05 12:43:35

  🔸 1f8c22b - Merged in bugfix/*********-7845/partnerNameMappingFIx (pull request #1089) [bugfix/*********-7845/partnerNameMappingFIx] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-05 10:52:42

  🔸 9578364 - CID 4.2.14 INT, TEST, UAT (cid)
     👤 Milan Černil <<EMAIL>> on 2025-05-02 20:58:58

  🔸 593d013 - Version 5.0.16-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-05-02 14:31:01

  🔸 9f9dabd - Version 5.0.15 (task)
     👤 quickbuild <<EMAIL>> on 2025-05-02 14:25:20

  🔸 16731d5 - BRUNO - push server config II (cid)
     👤 Milan Černil <<EMAIL>> on 2025-05-02 14:13:10

  🔸 72b478c - BRUNO - push server config (cid)
     👤 Milan Černil <<EMAIL>> on 2025-05-02 14:13:10

  🔸 b57bae8 - Merged in feature/disable-filebeat-for-designer (pull request #1083) [feature/disable-filebeat-for-designer] (feature)
     👤 Ján Manina <<EMAIL>> on 2025-05-02 14:13:10
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-02 14:13:10

  🔸 467b64b - *********-7836 - fix template doc70 (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-05-02 13:27:34
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-05-02 13:27:34

  🔸 e471ac1 - Merged in fix/ea-oauth (pull request #1073) [fix/ea-oauth] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-05-02 12:54:14
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-05-02 12:54:14

  🔸 b096fe0 - Merged in feature/*********-8062/50PAYERACCOUNT (pull request #1087) [feature/*********-8062/50PAYERACCOUNT] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-05-02 10:35:13

  🔸 82628fc - filebeat+prometheus disabled for fs envs (task)
     👤 jan_manina <<EMAIL>> on 2025-05-02 09:36:16
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-05-02 09:36:16

  🔸 b5e0123 - release version 4.2.8 -> 4.2.12 (task)
     👤 jan_manina <<EMAIL>> on 2025-05-02 09:36:16
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-05-02 09:36:16

  🔸 eee397a - Merged in feature/added-monitoring-prometheus-filebeat (pull request #1022) [feature/added-monitoring-prometheus-filebeat] (feature)
     👤 Ján Manina <<EMAIL>> on 2025-05-02 09:36:15
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-05-02 09:36:15

  🔸 cae152b - Merged in task/4.2/*********-8316/3DS-deep-link-adjustment (pull request #1084) [task/4.2/*********-8316/3DS-deep-link-adjustment] (task)
     👤 Milan Černil <<EMAIL>> on 2025-05-02 09:07:29

  🔸 0c206b6 - *********-7832 - mirror fixes in templates, add RU properties to fix nullpointer prints, fix resource-budle-for-csv (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-30 23:36:33
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-30 23:36:33

  🔸 538bd53 - CID: 4.2.13 (VB INT) (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-30 18:17:17

  🔸 27423ca - FE Lock docker versions (task)
     👤 Jan Dockal <<EMAIL>> on 2025-04-30 14:42:16

  🔸 1dddfa3 - *********-7814 - fix translates (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-30 11:42:17
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-30 11:42:17

  🔸 9940e08 - Merged in feature/*********-8304/mockLinks (pull request #1082) [feature/*********-8304/mockLinks] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-30 10:52:30

  🔸 9b35486 - Merged in feature/*********-8235/e2eFix (pull request #1081) [feature/*********-8235/e2eFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-30 10:51:58

  🔸 4e625e1 - Merged in bugfix/*********-7939/happyHourTimezonesFix (pull request #1078) [bugfix/*********-7939/happyHourTimezonesFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-30 10:51:49

  🔸 f4990fc - *********-7719 - fix template doc20 (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-29 21:56:54

  🔸 4766579 - CID: 4.2.12 - Finshape TEST, VB INT (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-29 12:44:43

  🔸 fc4bc80 - Version 5.0.15-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-29 12:23:45

  🔸 b8a767f - Version 5.0.14 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-29 12:18:24

  🔸 a328b72 - Merged in feature/*********-8108/openProductEligibility (pull request #1076) [feature/*********-8108/openProductEligibility] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-29 11:53:50

  🔸 25b2427 - Merged in bugifx/*********-7676/jenkinsfileFix (pull request #1075) [bugifx/*********-7676/jenkinsfileFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-29 11:53:49

  🔸 a4196ee - *********-7560 - optimize retry check status for IPS payments (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-29 11:53:44
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-04-29 11:53:44

  🔸 52c6691 - INT2 & UAT2 password update (task)
     👤 Milan Černil <<EMAIL>> on 2025-04-28 15:59:44

  🔸 73569f7 - Merged in feature/*********-7872/removeRemittanceInformationEnrichingForTrgDomesticPO (pull request #1071) [feature/*********-7872/removeRemittanceInformationEnrichingForTrgDomesticPO] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-28 07:19:27

  🔸 643485a - Merged in feature/*********-8047/reissueCardProductEligibilityValidator (pull request #1068) [feature/*********-8047/reissueCardProductEligibilityValidator] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-27 18:07:12

  🔸 294f398 - Merged in feature/NOJIRA/jenkinsfile (pull request #997) [feature/NOJIRA/jenkinsfile] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-27 18:05:39

  🔸 b2f6c46 - Merged in fix/e2e-test (pull request #1067) [fix/e2e-test] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 21:23:39

  🔸 89320cf - Merged in vb-ldap (pull request #1069) [vb-ldap] (task)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-04-25 20:52:33
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-04-25 20:52:33

  🔸 44db664 - Version 5.0.14-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-25 15:39:30

  🔸 11f2627 - Version 5.0.13 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-25 15:29:12

  🔸 f732b7f - build fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 15:16:23

  🔸 c371962 - Bruno - financials and nonfinancials removal after rename (task)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 14:16:16

  🔸 070e1e7 - Merged in fix/4.2/*********-7519/IPS-Movements-masked-partner-name (pull request #1066) [fix/4.2/*********-7519/IPS-Movements-masked-partner-name] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 14:16:15

  🔸 ac83a57 - Merged in fix/4.2/*********-8176/Closed_card_flow-error (pull request #1065) [fix/4.2/*********-8176/Closed_card_flow-error] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 14:16:15

  🔸 618dca1 - CID INT 4.2.9 (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-25 14:15:20

  🔸 275b86e - Merged in feature/*********-8096/documentUploadHandlingImprovement (pull request #1064) [feature/*********-8096/documentUploadHandlingImprovement] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-25 11:07:05

  🔸 f45fb4b - Version 5.0.13-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-24 15:55:38

  🔸 754c73e - Version 5.0.12 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-24 15:45:16

  🔸 cfe789e - CID: TEST: 4.2.9 (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-24 15:38:15

  🔸 0550a6d - *********-6859: Remaps card max limits (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-24 15:06:51

  🔸 f035cc4 - Merged in feature/4.2/*********-8075/BIC-shortcode (pull request #1063) [feature/4.2/*********-8075/BIC-shortcode] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-04-24 14:30:29

  🔸 44508ec - Merged in fix/*********-8087/releasing-payment-limit (pull request #1051) [fix/*********-8087/releasing-payment-limit] (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-24 14:23:55
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-04-24 14:23:55

  🔸 1589d25 - *********-8185 Client data mapping configuration - config (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-24 14:11:34
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-24 14:11:34

  🔸 ed2865f - *********-7319 - change IPS limits (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-24 12:11:10
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-24 12:11:10

  🔸 f19ccd5 - Merged in feature/*********-8167/openProductFeeAmountAndCurrencz (pull request #1056) [feature/*********-8167/openProductFeeAmountAndCurrencz] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-24 07:53:41

  🔸 cc3b44c - Merged in feature/*********-7971/happyHourExceededTranslationPlaceholder (pull request #1058) [feature/*********-7971/happyHourExceededTranslationPlaceholder] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-24 07:53:15

  🔸 a630b56 - Merged in feature/NOJIRA/generateDbosDomainRemoveDeprecations (pull request #1057) [feature/NOJIRA/generateDbosDomainRemoveDeprecations] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-24 07:53:13

  🔸 fa48c52 - Version 5.0.12-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-23 13:26:04

  🔸 6d47bb3 - Version 5.0.11 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-23 13:16:09

  🔸 491a5bd - NOJIRA - Checkstyle fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-23 13:14:53

  🔸 f78d133 - *********-7712 _ fix template doc12 (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-22 13:33:15
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-22 13:33:15

  🔸 203e31a - *********-7282: Added missing implements of CbsRestController (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-22 12:09:27

  🔸 ee91e96 - Merged in feature/*********-7993/improveRestLogging (pull request #1053) [feature/*********-7993/improveRestLogging] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-22 10:49:12

  🔸 c7c45f0 - *********-6859: Merge fix (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-22 09:04:49

  🔸 3745cfa - Merged in feature/4.2/*********-6859/WAY4-Max-value-for-card-limit (pull request #1044) [feature/4.2/*********-6859/WAY4-Max-value-for-card-limit] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-22 08:56:24

  🔸 693cd5e - Merged in bugfix/*********-8160/forexListenerForSameCurrencies (pull request #1052) [bugfix/*********-8160/forexListenerForSameCurrencies] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-22 08:48:37

  🔸 6afeb66 - CID INT2 & UAT2 5.0.10 (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-17 17:50:00

  🔸 87b6570 - CID INT & UAT 4.2.8 (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-17 17:49:47

  🔸 7c05267 - Version 5.0.11-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-17 17:35:55

  🔸 de726e4 - Version 5.0.10 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-17 17:25:24

  🔸 ae03f83 - Merged in feature/*********-8115/exb2.1.0 (pull request #1050) [feature/*********-8115/exb2.1.0] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-17 17:13:49

  🔸 185770e - Merged in feature/*********-8083/gdeMessageConcurrentUpdateHandling (pull request #1047) [feature/*********-8083/gdeMessageConcurrentUpdateHandling] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-17 17:13:44

  🔸 2c3bf1a - Merged in fix/4.2/*********-7939/Own-accounts-trasfer-Happy-and-forex (pull request #1049) [fix/4.2/*********-7939/Own-accounts-trasfer-Happy-and-forex] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-17 17:05:31

  🔸 427d864 - BRUNO/reorganize collections (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-17 12:02:24
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-17 12:02:24

  🔸 522426d - *********-5746 - remove unsued scripts from bruno, add local env (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-17 12:02:20
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-17 12:02:20

  🔸 8b29fda - Merged in feature/*********-7282/e2eFixes2 (pull request #1042) [feature/*********-7282/e2eFixes2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-17 11:50:23

  🔸 98b20c2 - Merged in fix/4.2/*********-8024/Search-in-Movements (pull request #1045) [fix/4.2/*********-8024/Search-in-Movements] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-17 09:59:46

  🔸 8c46ace - Merged in feature/4.2/*********-7961-GDE-optimization (pull request #1028) [feature/4.2/*********-7961-GDE-optimization] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-04-16 14:33:25

  🔸 fa7fe64 - CID: Fix wac2 (bugfix)
     👤 Jan Dockal <<EMAIL>> on 2025-04-15 18:05:27

  🔸 7aecf92 - Merged in fix/4.2/*********-8012/3DS-fixes (pull request #1041) [fix/4.2/*********-8012/3DS-fixes] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-14 14:53:00

  🔸 79b0ebc - Feature/4.2/*********-5746/migrate postman to bruno (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-14 14:41:50
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-14 14:41:50

  🔸 dda3ac4 - *********-7011 - fix reciept template, fix loading translated catalog values, fix floating in template (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-14 14:29:28
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-14 14:29:28

  🔸 7550abe - *********-7405 - fix validating dateFrom (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-14 13:42:47
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-14 13:42:47

  🔸 668589a - Feature/*********-7269/map ips movements reciepts to doc1 template (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-14 11:19:14

  🔸 0c1d923 - Merged in fix/4.2/*********-7673/Own-account-transfer-acc-not-found (pull request #1039) [fix/4.2/*********-7673/Own-account-transfer-acc-not-found] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-14 10:18:48

  🔸 82690ea - Version 5.0.10-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-11 17:27:35

  🔸 e2f1d54 - Version 5.0.9 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-11 17:17:38

  🔸 6b0a733 - Merged in feature/*********-7282/e2eFixes (pull request #1033) [feature/*********-7282/e2eFixes] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-11 16:36:02

  🔸 7cbb8a7 - CID: FE tags for DEV2 (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-04-10 16:38:49

  🔸 85a0d70 - Merged in feature/4.2/*********-7968-Card-replication-exception-catch (pull request #1031) [feature/4.2/*********-7968-Card-replication-exception-catch] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-04-10 15:07:06

  🔸 061f479 - Merged in feature/NOJIRA/uploadedDocumentCategoriesPaymentsFix (pull request #1030) [feature/NOJIRA/uploadedDocumentCategoriesPaymentsFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-10 14:47:42

  🔸 3cd6daa - *********-7733 - fix amount value format, fir mapping Nr. field (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-10 13:36:17
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-10 13:36:17

  🔸 1c01c09 - *********-7044 - fix align in table, fix mapping of credit used, payment due (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-10 13:36:15
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-10 13:36:15

  🔸 5da7b90 - Merged in feature/*********-7282/rabbitMqAndMovements (pull request #1015) [feature/*********-7282/rabbitMqAndMovements] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-10 13:26:56

  🔸 1c04b58 - NOJIRA: CID.md (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-10 07:28:55

  🔸 e455b53 - Version 5.0.9-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-10 06:51:31

  🔸 f3706ae - Version 5.0.8 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-10 06:41:47

  🔸 bd9124d - Branch replication - build fix (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-09 14:50:10

  🔸 e5c2aae - Branch replication - basic payload validation (task)
     👤 Milan Černil <<EMAIL>> on 2025-04-09 14:44:02

  🔸 9f06526 - CID: Lock CMS migration versions (cid)
     👤 Jan Dockal <<EMAIL>> on 2025-04-09 14:09:16

  🔸 12b804c - Merged in feature/*********-15/adjustStorePanMock (pull request #1017) [feature/*********-15/adjustStorePanMock] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-09 13:00:22

  🔸 b27aa22 - Merged in feature/*********-7599/uploadFileWithRrn (pull request #1020) [feature/*********-7599/uploadFileWithRrn] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-09 10:34:52

  🔸 ae0d583 - Merged in feature/*********-7589/salaryProjectUploadValueDate (pull request #1016) [feature/*********-7589/salaryProjectUploadValueDate] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-09 10:34:49

  🔸 cf7a793 - Merged in feature/*********-7474/feeWIthPaymentType (pull request #1021) [feature/*********-7474/feeWIthPaymentType] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-09 10:34:26

  🔸 f26af1c - Merged in fix/4.2/*********/*********-7608-open-product-message-error (pull request #1024) [fix/4.2/*********/*********-7608-open-product-message-error] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-09 10:00:22

  🔸 f9f751f - Merged in fix/4.2/*********-7701Payment-in-the-future (pull request #1023) [fix/4.2/*********-7701Payment-in-the-future] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-09 10:00:18

  🔸 88272ce - *********-7880 - fix mapping payment type code for IPS intrabank payments (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-09 09:49:16

  🔸 ccb8412 - Increase memory (task)
     👤 Jan Dockal <<EMAIL>> on 2025-04-08 09:48:52

  🔸 02afe05 - Merged in fix/*********-15/aliasInconsistencyErrorCodeTypoFix (pull request #1014) [fix/*********-15/aliasInconsistencyErrorCodeTypoFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-07 08:22:41

  🔸 14792d6 - Merged in fix/NOJIRA/cid-rabbitmq (pull request #1013) [fix/NOJIRA/cid-rabbitmq] (bugfix)
     👤 Jan Holec <<EMAIL>> on 2025-04-07 08:00:13
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-04-07 08:00:13

  🔸 7b3f238 - CID - finshape envs - rabbitmq queue host name (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-07 07:55:11

  🔸 10daa47 - Merged in web-cid (pull request #1011) [web-cid] (cid)
     👤 Jan Dočkal <<EMAIL>> on 2025-04-04 20:40:45

  🔸 2bbee46 - Merged in fix/4.2/*********-3831/Account-balances-npe (pull request #1012) [fix/4.2/*********-3831/Account-balances-npe] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-04 12:43:44

  🔸 cb8a4d8 - CID INT, TEST, UAT 4.2.6 (cid)
     👤 Milan Černil <<EMAIL>> on 2025-04-03 14:18:16

  🔸 b5d2941 - NOJIRA - CID fixes (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-03 14:18:16

  🔸 cf9570d - Version 5.0.8-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-04-03 14:06:17

  🔸 fa4b052 - Version 5.0.7 (task)
     👤 quickbuild <<EMAIL>> on 2025-04-03 13:56:32

  🔸 5dd7a34 - Merged in fix/4.2/*********-6847-CID-fixes (pull request #1001) [fix/4.2/*********-6847-CID-fixes] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-04-03 12:53:00

  🔸 03bc18d - Merged in feature/4.2/*********-7742-BE-change-of-the-replication-of-relatedAccount-on-Movements (pull request #1010) [feature/4.2/*********-7742-BE-change-of-the-replication-of-relatedAccount-on-Movements] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-04-03 12:53:00

  🔸 c3196a1 - Merged in feature/*********-7587/Mask-partner-name-for-IPS-debit-movements (pull request #990) [feature/*********-7587/Mask-partner-name-for-IPS-debit-movements] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-04-03 12:53:00

  🔸 ac5e768 - *********-7400 - add filtering out Victoria bank servicer (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-03 11:17:19
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-03 11:17:19

  🔸 24555bd - *********-7405 - adjust statementList templates to show "" instead of "-" or "null", handle potential NPE (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-03 10:56:02
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-03 10:56:02

  🔸 a15d70c - *********-7703 - fix parsing RTP request (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-03 10:56:00
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-03 10:56:00

  🔸 8a27afe - *********-7703 - fix thub flow for ips intrabank (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-03 10:55:58
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-03 10:55:58

  🔸 90d83db - NOJIRA: *********-7710: Merge fix (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-03 10:37:49

  🔸 b0d0655 - *********-7710: THUB 1.0.0-M.25 (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-03 10:32:45

  🔸 3190e77 - *********-3831: Fixes available balances by subtracting locked amount (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-02 09:28:03

  🔸 36423d2 - raised gaas mem limit to 3GB on vb-test env (task)
     👤 jan_manina <<EMAIL>> on 2025-04-01 12:33:53
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-04-01 12:33:53

  🔸 5f11255 - NOJIRA - update release version for vb-test Approved-by: Nikolas Charalambidis Approved-by: Milan Černil (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:54
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:54

  🔸 bfd5602 - *********-7155 - fix mapping creditor/debitor for incommint RPT (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:49
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:49

  🔸 6ddbebc - *********-7155 - fix ips pain 13 credit operation processor, fix gds query (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:42

  🔸 00b9410 - *********-7155 - fix ips pain 13 credit operation processor (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-04-01 12:24:33

  🔸 1c8d214 - Merged in bugfix/*********-7661/fixT24PaymentJsonResponse (pull request #998) [bugfix/*********-7661/fixT24PaymentJsonResponse] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-01 11:49:06

  🔸 605647f - Merged in feature/*********-6847/cidAndConfigFixes (pull request #996) [feature/*********-6847/cidAndConfigFixes] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-04-01 10:15:22

  🔸 ef43900 - *********-6847 fix of wrong tpm version deployment (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-31 15:39:10

  🔸 f52504a - Merged in feature/*********-6847-readme (pull request #995) [feature/*********-6847-readme] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-31 15:39:10

  🔸 d1e76bd - .gitignore (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-31 11:17:10

  🔸 6fb656d - *********-368: VB Finshape API 1.4.7 (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-31 09:45:10

  🔸 2c4fda9 - *********-6847 Create new INT2 environment & grafana - BE (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-29 13:19:13

  🔸 5f437b9 - Merged in feature/*********-7282/4.1/New-DEV-env-+-rabbitMQ (pull request #978) [feature/*********-7282/4.1/New-DEV-env-+-rabbitMQ] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-29 13:19:13

  🔸 b8fe46f - Merged in feature/*********-6847/new-INT2-environments (pull request #968) [feature/*********-6847/new-INT2-environments] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-29 13:17:53

  🔸 32a8d2d - Version 5.0.7-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-03-29 10:17:08

  🔸 6267097 - Version 5.0.6 (task)
     👤 quickbuild <<EMAIL>> on 2025-03-29 10:08:00

  🔸 40641e4 - Merged in feature/NOJIRA/5.0.6-SNAPSHOT (pull request #993) [feature/NOJIRA/5.0.6-SNAPSHOT] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-29 09:45:31
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-03-29 09:45:31

  🔸 8b03bac - Merged in bugfix/*********-345/5.0.4fixes (pull request #977) [bugfix/*********-345/5.0.4fixes] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-28 11:05:11

  🔸 4377ee3 - *********-7512: OpenAPI fix (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-28 10:56:29

  🔸 883ac01 - Merged in feature/*********-7512/replicationInfo (pull request #982) [feature/*********-7512/replicationInfo] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-28 10:53:12

  🔸 f689165 - *********-7237 - new thub flow for ips intrabank payment, rename liquibase script (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-28 09:36:14

  🔸 78de78d - Merged in NOJIRA/merge-ips-flow-from-4.1-to-develop (pull request #987) [NOJIRA/merge-ips-flow-from-4.1-to-develop] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-27 15:24:22

  🔸 8767d05 - NOJIRA - fix E2E test (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-27 10:20:17

  🔸 b6aadac - Merged in feature/*********-7501/Update-Attachment-Handling-II (pull request #989) [feature/*********-7501/Update-Attachment-Handling-II] (task)
     👤 Milan Černil <<EMAIL>> on 2025-03-27 09:56:15

  🔸 475f263 - Merged in feature/*********-7555/GAAS-&-GEN-hikari-pool (pull request #986) [feature/*********-7555/GAAS-&-GEN-hikari-pool] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-26 11:06:13

  🔸 22ff184 - Merged in feature/*********-7501/Update-Attachment-Handling-for-Swift-IntrabankFcyPO (pull request #984) [feature/*********-7501/Update-Attachment-Handling-for-Swift-IntrabankFcyPO] (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-26 09:46:16

  🔸 69da261 - Merged in fix/*********-7430/Treasury-account-wrong-decimal-separator-on-detail (pull request #985) [fix/*********-7430/Treasury-account-wrong-decimal-separator-on-detail] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-26 07:09:08

  🔸 ad737f8 - Merged in fix/*********-7493/Reissue-card-CBS-Payload (pull request #980) [fix/*********-7493/Reissue-card-CBS-Payload] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-24 14:43:16

  🔸 44a6a7d - Merged in feature/*********-7471/New-HSM-fun-configuration (pull request #974) [feature/*********-7471/New-HSM-fun-configuration] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-24 14:33:43

  🔸 037711d - *********-347 GAAS update 15.2.1 (task)
     👤 Milan Černil <<EMAIL>> on 2025-03-24 14:30:32

  🔸 8b481ee - Version 5.0.5-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-03-24 05:33:30

  🔸 231afbf - Version 5.0.4 (task)
     👤 quickbuild <<EMAIL>> on 2025-03-24 05:23:14

  🔸 178c819 - Merged in bugfix/nojira/spxOpenApiFix (pull request #975) [bugfix/nojira/spxOpenApiFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-24 05:20:08

  🔸 f3f013b - Approved-by: Nikolas Charalambidis Approved-by: Milan Černil (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-23 22:32:57
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-03-23 22:32:57

  🔸 0751055 - *********-7011 - fix loading city from GDE, add city into statements mapping (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-23 22:27:21
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-03-23 22:27:21

  🔸 059caa3 - *********-257 - add E2E test for card flow which are affected by Visa alias (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-23 21:30:26

  🔸 b00325b - Version 5.0.4-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-03-21 11:18:45

  🔸 f412d5d - Version 5.0.3 (task)
     👤 quickbuild <<EMAIL>> on 2025-03-21 11:10:00

  🔸 93c055f - Merged in feature/*********-266/acceptLanguage (pull request #945) [feature/*********-266/acceptLanguage] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-21 11:01:18

  🔸 818f46c - Merged in feature/*********-260/p2pPayment2 (pull request #969) [feature/*********-260/p2pPayment2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-21 10:33:57

  🔸 0d84d5b - Merged in fix/*********-7011/Receipt-for-individuals-payment-between-accounts (pull request #970) [fix/*********-7011/Receipt-for-individuals-payment-between-accounts] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-21 09:24:36

  🔸 0a5c3d3 - Merged in feature/*********-260/p2pPayment (pull request #950) [feature/*********-260/p2pPayment] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-21 04:06:09

  🔸 0358b73 - Merged in feature/*********-318/checkUserAliasFinetuning (pull request #967) [feature/*********-318/checkUserAliasFinetuning] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-20 11:39:56

  🔸 140e02c - NOJIRA - int2 dev for config deployer (cid)
     👤 Milan Černil <<EMAIL>> on 2025-03-20 07:11:01

  🔸 040a09a - Merged in feature/NOJIRA/way4native-more-info-for-gde (pull request #966) [feature/NOJIRA/way4native-more-info-for-gde] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-20 07:11:01

  🔸 2903a79 - Merged in bugfix/*********-334/ipsThubRetryDelayFix (pull request #965) [bugfix/*********-334/ipsThubRetryDelayFix] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-19 16:46:37

  🔸 1b7232c - Merged in feature/*********-335/vbFinshapeApi1.4.6 (pull request #961) [feature/*********-335/vbFinshapeApi1.4.6] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-19 08:58:32

  🔸 0b4ebf5 - CID: 4.1.21 (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-19 06:25:50

  🔸 e091c25 - Merged in bugfix/*********-7377/clientReplicationGdsFix2 (pull request #964) [bugfix/*********-7377/clientReplicationGdsFix2] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-19 06:25:47

  🔸 7de5e9d - CID: 4.1.20 (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-19 06:25:46

  🔸 0b7ee2a - Merged in feature/*********-7239/clientReplicationGdsFix (pull request #962) [feature/*********-7239/clientReplicationGdsFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-18 14:51:46

  🔸 11c35fb - Merged in bugfix/*********-7378/addedProductStatusOnMissingProductIdFix (pull request #963) [bugfix/*********-7378/addedProductStatusOnMissingProductIdFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-18 14:51:04

  🔸 cdb99c2 - Merged in feature/*********-7239/clientReplicationFnFieldsFix (pull request #960) [feature/*********-7239/clientReplicationFnFieldsFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-17 10:41:11

  🔸 0558cde - *********-7044 - fix dateFrom value in spx request (bugfix)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-17 10:34:22
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-03-17 10:34:22

  🔸 49b9a7a - Merged in feature/*********-322/patchSelectionSerializer (pull request #959) [feature/*********-322/patchSelectionSerializer] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-17 09:34:06

  🔸 ca1076a - Merged in feature/*********-7348/HolderFiscalCode-missing (pull request #958) [feature/*********-7348/HolderFiscalCode-missing] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-17 09:19:24

  🔸 6d22b2f - CID: 4.1.18 (cid)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-17 05:08:29

  🔸 1cfdab7 - NOJIRA: TODO: *********-322 (task)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-14 11:08:07

  🔸 9d44872 - *********-257 - adjuct cards flows by visa P2P alias, payment credentials udpates (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-13 19:04:20

  🔸 6816b59 - Merged in feature/*********-318/consistencyCheck (pull request #955) [feature/*********-318/consistencyCheck] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-13 15:28:44

  🔸 289fdf0 - Merged in fix/*********-7334/Issue-when-merchant-daily-statements-return-404 (pull request #954) [fix/*********-7334/Issue-when-merchant-daily-statements-return-404] (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-13 13:04:54

  🔸 e9366dd - Merged in feature/*********-251/createAliasE2E (pull request #943) [feature/*********-251/createAliasE2E] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-12 19:01:49

  🔸 a26664d - Merged in bugfix/*********-304/mockErrorHandling (pull request #948) [bugfix/*********-304/mockErrorHandling] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-12 15:40:43

  🔸 e8d9c49 - Merged in feature/*********-7239/clientReplicationFix (pull request #953) [feature/*********-7239/clientReplicationFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-12 15:40:08

  🔸 4531e07 - Merged in feature/*********-7239/clientReplication (pull request #941) [feature/*********-7239/clientReplication] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-12 11:56:41

  🔸 2d97e91 - Merged in fix/typo (pull request #951) [fix/typo] (bugfix)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-03-11 10:29:23

  🔸 476b9c6 - Merged in fix/cid/cid-2-compatibility (pull request #949) [fix/cid/cid-2-compatibility] (bugfix)
     👤 Roman Knotek <<EMAIL>> on 2025-03-11 10:25:56
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-03-11 10:25:56

  🔸 771e446 - Merged in bugfix/*********-304/mockErrorHandling (pull request #946) [bugfix/*********-304/mockErrorHandling] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-10 10:12:19

  🔸 eae9a0b - Merged in feature/*********-7175/rabbitMqDeadLettering (pull request #940) [feature/*********-7175/rabbitMqDeadLettering] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-08 17:23:08

  🔸 e017d86 - *********-252 - create card p2p alias, adjust card mask enricher (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-07 22:15:55

  🔸 9a2c3d8 - Merged in feature/*********-7122/Treasury-IBAN (pull request #944) [feature/*********-7122/Treasury-IBAN] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-07 11:51:42

  🔸 eb821ea - Merged in feat/vb-ldap (pull request #942) [feat/vb-ldap] (feature)
     👤 Tomáš Lipovský <<EMAIL>> on 2025-03-07 10:28:29
     🔧 Committed by Milan Černil <<EMAIL>> on 2025-03-07 10:28:29

  🔸 a7cb587 - NOJIRA - app auth url for 3ds adjusted (task)
     👤 Milan Černil <<EMAIL>> on 2025-03-05 12:43:06

  🔸 3a55d94 - NOJIRA - fix GAAS liquibase (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-05 10:49:13

  🔸 5b1e177 - NOJIRA - fix GDE deployment (bugfix)
     👤 Milan Černil <<EMAIL>> on 2025-03-05 08:52:21

  🔸 f703a9d - Version 5.0.3-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-03-04 14:54:54

  🔸 25b972b - Version 5.0.2 (task)
     👤 quickbuild <<EMAIL>> on 2025-03-04 14:41:46

  🔸 48d5c28 - Merged in feature/*********-251/visaAdsMock2 (pull request #936) [feature/*********-251/visaAdsMock2] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-04 14:11:40

  🔸 3de5e80 - NOJIRA: Merge from release/4.1 (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-04 14:10:31
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-03-04 14:10:31

  🔸 e219578 - Merged in feature/*********-259/visaAdsSecurityFix (pull request #937) [feature/*********-259/visaAdsSecurityFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-04 13:54:18

  🔸 e53ac0f - Merged in feature/*********-290/5.0.1-fixes (pull request #933) [feature/*********-290/5.0.1-fixes] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-04 13:53:58

  🔸 2719597 - Merged in feature/*********-198/spxBusinessApiFix (pull request #935) [feature/*********-198/spxBusinessApiFix] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-04 09:17:55

  🔸 f29aa3d - Version 5.0.2-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-03-03 14:56:41

  🔸 9dccf67 - Version 5.0.1 (task)
     👤 quickbuild <<EMAIL>> on 2025-03-03 14:45:58

  🔸 75aa5ce - Merged in feature/*********-255/delete-visa-alias (pull request #931) [feature/*********-255/delete-visa-alias] (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-03-03 14:44:34
     🔧 Committed by Nikolas Charalambidis <<EMAIL>> on 2025-03-03 14:44:34

  🔸 e9f9351 - Merged in feature/*********-251/visaAdsMock (pull request #929) [feature/*********-251/visaAdsMock] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-03-03 11:06:36

  🔸 21250a7 - Merged in fix/release4.1/*********-7173/new-card-request---illegal-currency (pull request #928) - fix yaml version [fix/release4.1/*********-7173/new-card-request---illegal-currency] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-03 07:20:35

  🔸 c569f0f - Merged in fix/release4.1/*********-7173/new-card-request---illegal-currency (pull request #928) [fix/release4.1/*********-7173/new-card-request---illegal-currency] (feature)
     👤 Milan Černil <<EMAIL>> on 2025-03-03 07:14:11

  🔸 d508f3f - Merged in feature/*********-251/createP2PAlias (pull request #923) [feature/*********-251/createP2PAlias] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-02-28 19:00:54

  🔸 ff0be14 - Merged in bugfix/*********-274/5.0.0-fixes (pull request #927) [bugfix/*********-274/5.0.0-fixes] (bugfix)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-02-28 06:12:04

  🔸 472f2fc - Merge branch 'release/4.1' into develop (task)
     👤 Jakub Jirsa <<EMAIL>> on 2025-02-27 12:38:34

  🔸 266d45e - Fix/*********-7173/add filtering pruduct catalog values by currency (feature)
     👤 Jakub Jirsa <<EMAIL>> on 2025-02-27 11:07:13
     🔧 Committed by Jakub Jirsa <<EMAIL>> on 2025-02-27 11:07:13

  🔸 b2d183f - Merged in feature/*********-272/removeDeprecatedEndpoints (pull request #926) [feature/*********-272/removeDeprecatedEndpoints] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-02-27 08:45:05

  🔸 a7da025 - Version 5.0.1-SNAPSHOT (task)
     👤 quickbuild <<EMAIL>> on 2025-02-26 14:59:08

  🔸 7462ef4 - Version 5.0.0 (task)
     👤 quickbuild <<EMAIL>> on 2025-02-26 14:48:36

  🔸 ba11d5d - Merged in feature/*********-198/createP2PAlias (pull request #885) [feature/*********-198/createP2PAlias] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-02-26 10:47:13

  🔸 a097878 - NOJIRA - RabbitMqSystemIdentification for Branches route out operation (task)
     👤 Milan Černil <<EMAIL>> on 2025-02-26 10:21:09

  🔸 0cb5a9c - Merged in feature/NOJIRA/5.0 (pull request #924) [feature/NOJIRA/5.0] (feature)
     👤 Nikolas Charalambidis <<EMAIL>> on 2025-02-26 10:03:32


================================================================================
End of Git Release Log
================================================================================
📈 SUMMARY:
   Total Tags: 3
   Total Commits: 491
   Average Commits per Tag: 163.7
