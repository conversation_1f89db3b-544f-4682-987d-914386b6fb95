<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.nikolascharalambidis</groupId>
    <artifactId>git-rellog-maven-plugin</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>maven-plugin</packaging>

    <developers>
        <developer>
            <id>Nichar</id>
            <name><PERSON><PERSON></name>
            <email><EMAIL></email>
        </developer>
        <!-- Additional developers... -->
    </developers>

    <organization>
        <name><PERSON><PERSON></name>
        <url>https://www.nikolas-charalambidis.com</url>
    </organization>

    <!-- FIXME change it to the project's website -->
    <url>https://www.nikolas-charalambidis.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.source>17</maven.compiler.source>

        <!-- Dependencies -->
        <maven.version>3.5.4</maven.version>
        <!-- the same as Maven 3.5.4 -->
        <aether.version>1.1.0</aether.version>
        <plexus-utils.version>4.0.0</plexus-utils.version>
        <plexus-xml.version>4.0.3</plexus-xml.version>
        <plexus-classworlds.version>2.8.0</plexus-classworlds.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <org.eclipse.sisu.version>0.9.0.M2</org.eclipse.sisu.version>
        <maven-plugin-annotations.version>3.11.0</maven-plugin-annotations.version>
        <org.apache.ant.version>1.10.14</org.apache.ant.version>
        <jgit.version>6.7.0.202309050840-r</jgit.version>


        <!--        <maven-core.version>3.9.6</maven-core.version>-->
        <!--        <maven-plugin-api.version>3.9.6</maven-plugin-api.version>-->
        <!--        <maven-project.version>2.2.1</maven-project.version>-->
        <junit-jupiter.version>5.10.1</junit-jupiter.version>
        <itf.version>0.13.1</itf.version>
        <log4j.version>2.22.0</log4j.version>
        <reflections.version>0.10.2</reflections.version>
        <javaparser.version>3.25.8</javaparser.version>
        <lombok.version>1.18.30</lombok.version>
        <javax.inject>1</javax.inject>

        <!-- Plugins -->
        <maven-compiler-plugin.version>3.12.1</maven-compiler-plugin.version>
        <!--        <maven-plugin-plugin.version>3.10.2</maven-plugin-plugin.version>-->
        <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
        <!--        <maven-javadoc-plugin.version>3.6.3</maven-javadoc-plugin.version>-->
        <!--        <maven-project-info-reports-plugin.version>3.5.0</maven-project-info-reports-plugin.version>-->
        <!--        <maven-plugin-report-plugin.version>3.10.2</maven-plugin-report-plugin.version>-->

        <!--
        Maven version 3.9.2 and higher identifies plugins that might break upon migration to 4.X.
        See: https://stackoverflow.com/q/76355897/3764965
        See: https://maven.apache.or
         g/docs/3.9.2/release-notes.html
        -->
    </properties>

    <dependencies>
        <!-- Maven -->
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-plugin-api</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-core</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-model</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-model-builder</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-settings</artifactId>
            <version>${maven.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.plugin-tools</groupId>
            <artifactId>maven-plugin-annotations</artifactId>
            <version>${maven-plugin-annotations.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.aether</groupId>
            <artifactId>aether-api</artifactId>
            <version>${aether.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.aether</groupId>
            <artifactId>aether-util</artifactId>
            <version>${aether.version}</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.sisu</groupId>
            <artifactId>org.eclipse.sisu.plexus</artifactId>
            <version>${org.eclipse.sisu.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-classworlds</artifactId>
            <version>${plexus-classworlds.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <!--
            Dependency injection instead of Plexus.
            See: https://maven.apache.org/maven-jsr330.html
            -->
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>${javax.inject}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-utils</artifactId>
            <version>${plexus-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-xml</artifactId>
            <version>${plexus-xml.version}</version>
        </dependency>

        <!-- JGit for Git operations -->
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit</artifactId>
            <version>${jgit.version}</version>
        </dependency>

        <!-- Lombok for reducing boilerplate code -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Freemarker for templating -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.32</version>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- ITF (Integration Test Framework) dependencies -->
        <dependency>
            <groupId>com.soebes.itf.jupiter.extension</groupId>
            <artifactId>itf-jupiter-extension</artifactId>
            <version>${itf.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.soebes.itf.jupiter.extension</groupId>
            <artifactId>itf-assertj</artifactId>
            <version>${itf.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.soebes.itf.jupiter.extension</groupId>
            <artifactId>itf-extension-maven</artifactId>
            <version>${itf.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-plugin-plugin</artifactId>
                <version>3.10.2</version>
                <configuration>
                    <goalPrefix>git-rellog</goalPrefix>
                </configuration>
            </plugin>

            <!-- ITF Maven Plugin for Integration Tests -->
            <plugin>
                <groupId>com.soebes.itf.jupiter.extension</groupId>
                <artifactId>itf-maven-plugin</artifactId>
                <version>${itf.version}</version>
                <executions>
                    <execution>
                        <id>installing</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>install</goal>
                            <goal>resources-its</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Failsafe Plugin for Integration Tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <systemProperties>
                        <maven.version>${maven.version}</maven.version>
                        <maven.home>${maven.home}</maven.home>
                    </systemProperties>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
