package com.nikolascharalambidis;

import com.nikolascharalambidis.formatter.ConsoleOutputFormatter;
import com.nikolascharalambidis.model.TagInfo;
import com.nikolascharalambidis.service.GitService;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.LifecyclePhase;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;
import org.apache.maven.project.MavenProject;

import java.io.File;
import java.util.List;

/**
 * Maven plugin goal that generates a Git release log showing commits organized by tags.
 * 
 * This mojo retrieves Git history from the current repository and displays commits
 * grouped by their associated tags, providing a clear view of what changes were
 * included in each release.
 */
@Mojo(name = "generate", defaultPhase = LifecyclePhase.COMPILE, threadSafe = true)
public class GitRellogMojo extends AbstractMojo {
    
    /**
     * The Maven project instance.
     */
    @Parameter(defaultValue = "${project}", readonly = true, required = true)
    private MavenProject project;
    
    /**
     * The directory containing the Git repository.
     * Defaults to the project's base directory.
     */
    @Parameter(property = "git.repository.dir", defaultValue = "${project.basedir}")
    private File repositoryDirectory;
    
    /**
     * Whether to include a summary at the end of the output.
     */
    @Parameter(property = "git.rellog.includeSummary", defaultValue = "true")
    private boolean includeSummary;
    
    /**
     * Whether to skip the plugin execution.
     */
    @Parameter(property = "git.rellog.skip", defaultValue = "false")
    private boolean skip;
    
    /**
     * Maximum number of tags to process. Set to -1 for no limit.
     */
    @Parameter(property = "git.rellog.maxTags", defaultValue = "-1")
    private int maxTags;
    
    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        if (skip) {
            getLog().info("Git release log generation skipped.");
            return;
        }
        
        if (repositoryDirectory == null || !repositoryDirectory.exists()) {
            throw new MojoExecutionException("Repository directory does not exist: " + repositoryDirectory);
        }
        
        File gitDir = new File(repositoryDirectory, ".git");
        if (!gitDir.exists()) {
            throw new MojoExecutionException("Not a Git repository: " + repositoryDirectory);
        }
        
        getLog().info("Generating Git release log for repository: " + repositoryDirectory.getAbsolutePath());
        
        try (GitService gitService = new GitService(repositoryDirectory)) {
            List<TagInfo> tagInfos = gitService.getTagsWithCommits();
            
            // Limit the number of tags if specified
            if (maxTags > 0 && tagInfos.size() > maxTags) {
                tagInfos = tagInfos.subList(0, maxTags);
                getLog().info("Limited output to " + maxTags + " most recent tags.");
            }
            
            ConsoleOutputFormatter formatter = new ConsoleOutputFormatter();
            formatter.printTagsWithCommits(tagInfos);
            
            if (includeSummary) {
                formatter.printSummary(tagInfos);
            }
            
            getLog().info("Git release log generation completed successfully.");
            
        } catch (Exception e) {
            getLog().error("Failed to generate Git release log", e);
            throw new MojoExecutionException("Failed to generate Git release log: " + e.getMessage(), e);
        }
    }
}
