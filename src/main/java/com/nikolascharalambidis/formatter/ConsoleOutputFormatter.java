package com.nikolascharalambidis.formatter;

import com.nikolascharalambidis.model.CommitInfo;
import com.nikolascharalambidis.model.TagInfo;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Formatter for console output of Git release log information.
 */
public class ConsoleOutputFormatter {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String SEPARATOR = "=" .repeat(80);
    private static final String SUB_SEPARATOR = "-".repeat(40);
    
    /**
     * Formats and prints the tag information with commits to console.
     * 
     * @param tagInfos List of tag information to format
     */
    public void printTagsWithCommits(List<TagInfo> tagInfos) {
        if (tagInfos.isEmpty()) {
            System.out.println("No tags or commits found in the repository.");
            return;
        }
        
        System.out.println(SEPARATOR);
        System.out.println("GIT RELEASE LOG");
        System.out.println(SEPARATOR);
        System.out.println();
        
        for (TagInfo tagInfo : tagInfos) {
            printTagInfo(tagInfo);
            System.out.println();
        }
        
        System.out.println(SEPARATOR);
        System.out.println("End of Git Release Log");
        System.out.println(SEPARATOR);
    }
    
    private void printTagInfo(TagInfo tagInfo) {
        // Print tag header
        System.out.println("📋 TAG: " + tagInfo.getName());
        
        if (tagInfo.getTimestamp() != null) {
            String formattedDate = tagInfo.getTimestamp()
                    .atZone(ZoneId.systemDefault())
                    .format(DATE_FORMATTER);
            System.out.println("📅 Date: " + formattedDate);
        }
        
        if (tagInfo.isAnnotated() && tagInfo.getMessage() != null && !tagInfo.getMessage().trim().isEmpty()) {
            System.out.println("💬 Message: " + tagInfo.getMessage().trim());
        }
        
        if (tagInfo.getTaggerName() != null) {
            System.out.println("👤 Tagger: " + tagInfo.getTaggerName() + 
                    (tagInfo.getTaggerEmail() != null ? " <" + tagInfo.getTaggerEmail() + ">" : ""));
        }
        
        if (tagInfo.getTargetCommitHash() != null) {
            System.out.println("🎯 Target Commit: " + tagInfo.getTargetCommitHash().substring(0, 7));
        }
        
        System.out.println("📊 Commits: " + tagInfo.getCommits().size());
        System.out.println(SUB_SEPARATOR);
        
        // Print commits
        if (tagInfo.getCommits().isEmpty()) {
            System.out.println("  No commits found for this tag.");
        } else {
            for (CommitInfo commit : tagInfo.getCommits()) {
                printCommitInfo(commit);
            }
        }
    }
    
    private void printCommitInfo(CommitInfo commit) {
        String formattedDate = commit.getTimestamp()
                .atZone(ZoneId.systemDefault())
                .format(DATE_FORMATTER);
        
        System.out.printf("  🔸 %s - %s%n", commit.getShortHash(), commit.getMessage());
        System.out.printf("     👤 %s <%s> on %s%n", 
                commit.getAuthorName(), 
                commit.getAuthorEmail(), 
                formattedDate);
        
        // Show committer info if different from author
        if (!commit.getAuthorName().equals(commit.getCommitterName()) || 
            !commit.getAuthorEmail().equals(commit.getCommitterEmail())) {
            String committerDate = commit.getCommitterTimestamp()
                    .atZone(ZoneId.systemDefault())
                    .format(DATE_FORMATTER);
            System.out.printf("     🔧 Committed by %s <%s> on %s%n",
                    commit.getCommitterName(),
                    commit.getCommitterEmail(),
                    committerDate);
        }
        
        System.out.println();
    }
    
    /**
     * Prints a summary of the release log.
     * 
     * @param tagInfos List of tag information
     */
    public void printSummary(List<TagInfo> tagInfos) {
        if (tagInfos.isEmpty()) {
            return;
        }
        
        int totalTags = (int) tagInfos.stream().filter(tag -> !tag.getName().startsWith("Untagged") && !tag.getName().startsWith("All")).count();
        int totalCommits = tagInfos.stream().mapToInt(tag -> tag.getCommits().size()).sum();
        
        System.out.println("📈 SUMMARY:");
        System.out.println("   Total Tags: " + totalTags);
        System.out.println("   Total Commits: " + totalCommits);
        
        if (totalTags > 0) {
            double avgCommitsPerTag = (double) totalCommits / totalTags;
            System.out.printf("   Average Commits per Tag: %.1f%n", avgCommitsPerTag);
        }
    }
}
