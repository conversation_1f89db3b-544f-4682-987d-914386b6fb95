package com.nikolascharalambidis.model;

import lombok.Data;
import lombok.Builder;
import java.time.Instant;

/**
 * Represents information about a Git commit.
 */
@Data
@Builder
public class CommitInfo {
    
    /**
     * The commit hash (SHA-1).
     */
    private String hash;
    
    /**
     * The short commit hash (first 7 characters).
     */
    private String shortHash;
    
    /**
     * The commit message.
     */
    private String message;
    
    /**
     * The author name.
     */
    private String authorName;
    
    /**
     * The author email.
     */
    private String authorEmail;
    
    /**
     * The commit timestamp.
     */
    private Instant timestamp;
    
    /**
     * The committer name (may differ from author).
     */
    private String committerName;
    
    /**
     * The committer email.
     */
    private String committerEmail;
    
    /**
     * The commit timestamp by committer.
     */
    private Instant committerTimestamp;
}
