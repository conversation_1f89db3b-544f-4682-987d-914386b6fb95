package com.nikolascharalambidis.model;

import lombok.Data;
import lombok.Builder;
import java.time.Instant;
import java.util.List;

/**
 * Represents information about a Git tag and its associated commits.
 */
@Data
@Builder
public class TagInfo {
    
    /**
     * The tag name.
     */
    private String name;
    
    /**
     * The tag message (for annotated tags).
     */
    private String message;
    
    /**
     * The commit hash that this tag points to.
     */
    private String targetCommitHash;
    
    /**
     * The timestamp when the tag was created.
     */
    private Instant timestamp;
    
    /**
     * The tagger name (for annotated tags).
     */
    private String taggerName;
    
    /**
     * The tagger email (for annotated tags).
     */
    private String taggerEmail;
    
    /**
     * List of commits associated with this tag.
     * For release tags, this typically includes commits since the previous tag.
     */
    private List<CommitInfo> commits;
    
    /**
     * Whether this is an annotated tag or a lightweight tag.
     */
    private boolean annotated;
}
