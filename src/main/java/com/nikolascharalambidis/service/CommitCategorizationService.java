package com.nikolascharalambidis.service;

import org.apache.commons.text.similarity.LevenshteinDistance;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for categorizing commits and extracting branch information.
 */
public class CommitCategorizationService {
    
    private final Map<String, List<String>> categoryKeywords;
    private final int levenshteinThreshold;
    private final LevenshteinDistance levenshteinDistance;
    
    // Regex patterns for extracting branch names from Bitbucket merge messages
    private static final Pattern BITBUCKET_MERGE_PATTERN = Pattern.compile(
            "Merged in ([^\\s]+).*?\\(pull request #\\d+\\)", 
            Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern BRANCH_NAME_PATTERN = Pattern.compile(
            "([^/]+)/([^/]+)/(.+)", 
            Pattern.CASE_INSENSITIVE
    );
    
    public CommitCategorizationService(Map<String, List<String>> categoryKeywords, int levenshteinThreshold) {
        this.categoryKeywords = categoryKeywords;
        this.levenshteinThreshold = levenshteinThreshold;
        this.levenshteinDistance = new LevenshteinDistance(levenshteinThreshold);
    }
    
    /**
     * Extracts branch name from Bitbucket merge commit message.
     * 
     * @param commitMessage The commit message
     * @return The extracted branch name or null if not found
     */
    public String extractBranchName(String commitMessage) {
        if (commitMessage == null || commitMessage.trim().isEmpty()) {
            return null;
        }
        
        Matcher mergeMatcher = BITBUCKET_MERGE_PATTERN.matcher(commitMessage);
        if (mergeMatcher.find()) {
            return mergeMatcher.group(1);
        }
        
        return null;
    }
    
    /**
     * Categorizes a commit based on its message and branch name.
     * 
     * @param commitMessage The commit message
     * @param branchName The branch name (can be null)
     * @return The category or "uncategorized" if no match found
     */
    public String categorizeCommit(String commitMessage, String branchName) {
        if (commitMessage == null) {
            commitMessage = "";
        }
        
        String textToAnalyze = commitMessage.toLowerCase();
        if (branchName != null) {
            textToAnalyze += " " + branchName.toLowerCase();
        }
        
        // First try exact keyword matching
        for (Map.Entry<String, List<String>> entry : categoryKeywords.entrySet()) {
            String category = entry.getKey();
            List<String> keywords = entry.getValue();
            
            for (String keyword : keywords) {
                if (textToAnalyze.contains(keyword.toLowerCase())) {
                    return category;
                }
            }
        }
        
        // If no exact match, try Levenshtein distance matching
        if (levenshteinThreshold > 0) {
            for (Map.Entry<String, List<String>> entry : categoryKeywords.entrySet()) {
                String category = entry.getKey();
                List<String> keywords = entry.getValue();
                
                for (String keyword : keywords) {
                    if (isLevenshteinMatch(textToAnalyze, keyword.toLowerCase())) {
                        return category;
                    }
                }
            }
        }
        
        // Try to extract category from branch name pattern (e.g., feature/category/description)
        if (branchName != null) {
            String categoryFromBranch = extractCategoryFromBranchName(branchName);
            if (categoryFromBranch != null && categoryKeywords.containsKey(categoryFromBranch)) {
                return categoryFromBranch;
            }
        }
        
        return "uncategorized";
    }
    
    /**
     * Extracts category from branch name using common patterns.
     * 
     * @param branchName The branch name
     * @return The extracted category or null if not found
     */
    private String extractCategoryFromBranchName(String branchName) {
        if (branchName == null) {
            return null;
        }
        
        Matcher branchMatcher = BRANCH_NAME_PATTERN.matcher(branchName);
        if (branchMatcher.find()) {
            String potentialCategory = branchMatcher.group(1).toLowerCase();
            
            // Check if the extracted part matches any of our categories
            for (String category : categoryKeywords.keySet()) {
                if (category.equalsIgnoreCase(potentialCategory)) {
                    return category;
                }
            }
        }
        
        // Check if branch name starts with any category
        String lowerBranchName = branchName.toLowerCase();
        for (String category : categoryKeywords.keySet()) {
            if (lowerBranchName.startsWith(category.toLowerCase() + "/") || 
                lowerBranchName.startsWith(category.toLowerCase() + "-") ||
                lowerBranchName.startsWith(category.toLowerCase() + "_")) {
                return category;
            }
        }
        
        return null;
    }
    
    /**
     * Checks if two strings match within the Levenshtein distance threshold.
     * 
     * @param text The text to check
     * @param keyword The keyword to match against
     * @return true if they match within threshold
     */
    private boolean isLevenshteinMatch(String text, String keyword) {
        // Split text into words and check each word
        String[] words = text.split("\\s+");
        
        for (String word : words) {
            if (word.length() >= keyword.length() - levenshteinThreshold) {
                Integer distance = levenshteinDistance.apply(word, keyword);
                if (distance != null && distance <= levenshteinThreshold) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
