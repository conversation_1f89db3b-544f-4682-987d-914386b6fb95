package com.nikolascharalambidis.service;

import com.nikolascharalambidis.model.CommitInfo;
import com.nikolascharalambidis.model.TagInfo;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevTag;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;

import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for Git operations using JGit.
 */
public class GitService {
    
    private final Repository repository;
    private final Git git;
    
    public GitService(File repositoryDir) throws IOException {
        this.repository = new FileRepositoryBuilder()
                .setGitDir(new File(repositoryDir, ".git"))
                .readEnvironment()
                .findGitDir()
                .build();
        this.git = new Git(repository);
    }
    
    /**
     * Retrieves all tags with their associated commits.
     * 
     * @return List of TagInfo objects, sorted by tag creation date (newest first)
     * @throws GitAPIException if Git operations fail
     * @throws IOException if I/O operations fail
     */
    public List<TagInfo> getTagsWithCommits() throws GitAPIException, IOException {
        List<Ref> tags = git.tagList().call();
        List<TagInfo> tagInfos = new ArrayList<>();
        
        try (RevWalk revWalk = new RevWalk(repository)) {
            // Sort tags by creation date
            List<Ref> sortedTags = tags.stream()
                    .sorted((tag1, tag2) -> {
                        try {
                            Instant time1 = getTagTimestamp(tag1, revWalk);
                            Instant time2 = getTagTimestamp(tag2, revWalk);
                            return time2.compareTo(time1); // Newest first
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .collect(Collectors.toList());
            
            for (int i = 0; i < sortedTags.size(); i++) {
                Ref tag = sortedTags.get(i);
                Ref previousTag = (i < sortedTags.size() - 1) ? sortedTags.get(i + 1) : null;
                
                TagInfo tagInfo = createTagInfo(tag, previousTag, revWalk);
                tagInfos.add(tagInfo);
            }
            
            // Add commits that are not associated with any tag
            if (!sortedTags.isEmpty()) {
                TagInfo untaggedCommits = getUntaggedCommits(sortedTags.get(sortedTags.size() - 1), revWalk);
                if (!untaggedCommits.getCommits().isEmpty()) {
                    tagInfos.add(untaggedCommits);
                }
            } else {
                // No tags exist, get all commits
                TagInfo allCommits = getAllCommits(revWalk);
                if (!allCommits.getCommits().isEmpty()) {
                    tagInfos.add(allCommits);
                }
            }
        }
        
        return tagInfos;
    }
    
    private TagInfo createTagInfo(Ref tag, Ref previousTag, RevWalk revWalk) throws IOException {
        ObjectId tagObjectId = tag.getPeeledObjectId() != null ? tag.getPeeledObjectId() : tag.getObjectId();
        RevCommit tagCommit = revWalk.parseCommit(tagObjectId);
        
        String tagName = tag.getName().replace("refs/tags/", "");
        boolean isAnnotated = tag.getPeeledObjectId() != null;
        
        TagInfo.TagInfoBuilder builder = TagInfo.builder()
                .name(tagName)
                .targetCommitHash(tagCommit.getName())
                .annotated(isAnnotated);
        
        // Handle annotated tags
        if (isAnnotated) {
            try {
                RevTag revTag = revWalk.parseTag(tag.getObjectId());
                builder.message(revTag.getFullMessage())
                       .timestamp(Instant.ofEpochSecond(revTag.getTaggerIdent().getWhen().getTime() / 1000))
                       .taggerName(revTag.getTaggerIdent().getName())
                       .taggerEmail(revTag.getTaggerIdent().getEmailAddress());
            } catch (Exception e) {
                // Fallback to commit timestamp for lightweight tags
                builder.timestamp(Instant.ofEpochSecond(tagCommit.getCommitTime()));
            }
        } else {
            builder.timestamp(Instant.ofEpochSecond(tagCommit.getCommitTime()));
        }
        
        // Get commits between this tag and the previous tag
        List<CommitInfo> commits = getCommitsBetweenTags(tagCommit, previousTag, revWalk);
        builder.commits(commits);
        
        return builder.build();
    }
    
    private List<CommitInfo> getCommitsBetweenTags(RevCommit tagCommit, Ref previousTag, RevWalk revWalk) throws IOException {
        List<CommitInfo> commits = new ArrayList<>();
        
        if (previousTag != null) {
            ObjectId previousTagObjectId = previousTag.getPeeledObjectId() != null ? 
                    previousTag.getPeeledObjectId() : previousTag.getObjectId();
            RevCommit previousTagCommit = revWalk.parseCommit(previousTagObjectId);
            
            // Get commits between the two tags
            revWalk.reset();
            revWalk.markStart(tagCommit);
            revWalk.markUninteresting(previousTagCommit);
            
            for (RevCommit commit : revWalk) {
                commits.add(createCommitInfo(commit));
            }
        } else {
            // This is the oldest tag, get all commits up to this tag
            revWalk.reset();
            revWalk.markStart(tagCommit);
            
            for (RevCommit commit : revWalk) {
                commits.add(createCommitInfo(commit));
            }
        }
        
        return commits;
    }
    
    private TagInfo getUntaggedCommits(Ref oldestTag, RevWalk revWalk) throws IOException {
        ObjectId oldestTagObjectId = oldestTag.getPeeledObjectId() != null ? 
                oldestTag.getPeeledObjectId() : oldestTag.getObjectId();
        RevCommit oldestTagCommit = revWalk.parseCommit(oldestTagObjectId);
        
        // Get HEAD commit
        ObjectId headId = repository.resolve("HEAD");
        if (headId == null) {
            return TagInfo.builder()
                    .name("Untagged commits")
                    .commits(new ArrayList<>())
                    .build();
        }
        
        RevCommit headCommit = revWalk.parseCommit(headId);
        
        List<CommitInfo> commits = new ArrayList<>();
        revWalk.reset();
        revWalk.markStart(headCommit);
        revWalk.markUninteresting(oldestTagCommit);
        
        for (RevCommit commit : revWalk) {
            commits.add(createCommitInfo(commit));
        }
        
        return TagInfo.builder()
                .name("Untagged commits")
                .commits(commits)
                .timestamp(Instant.now())
                .build();
    }
    
    private TagInfo getAllCommits(RevWalk revWalk) throws IOException {
        ObjectId headId = repository.resolve("HEAD");
        if (headId == null) {
            return TagInfo.builder()
                    .name("All commits")
                    .commits(new ArrayList<>())
                    .build();
        }
        
        RevCommit headCommit = revWalk.parseCommit(headId);
        
        List<CommitInfo> commits = new ArrayList<>();
        revWalk.reset();
        revWalk.markStart(headCommit);
        
        for (RevCommit commit : revWalk) {
            commits.add(createCommitInfo(commit));
        }
        
        return TagInfo.builder()
                .name("All commits")
                .commits(commits)
                .timestamp(Instant.now())
                .build();
    }
    
    private CommitInfo createCommitInfo(RevCommit commit) {
        return CommitInfo.builder()
                .hash(commit.getName())
                .shortHash(commit.getName().substring(0, 7))
                .message(commit.getShortMessage())
                .authorName(commit.getAuthorIdent().getName())
                .authorEmail(commit.getAuthorIdent().getEmailAddress())
                .timestamp(Instant.ofEpochSecond(commit.getCommitTime()))
                .committerName(commit.getCommitterIdent().getName())
                .committerEmail(commit.getCommitterIdent().getEmailAddress())
                .committerTimestamp(Instant.ofEpochSecond(commit.getCommitterIdent().getWhen().getTime() / 1000))
                .build();
    }
    
    private Instant getTagTimestamp(Ref tag, RevWalk revWalk) throws IOException {
        if (tag.getPeeledObjectId() != null) {
            // Annotated tag
            try {
                RevTag revTag = revWalk.parseTag(tag.getObjectId());
                return Instant.ofEpochSecond(revTag.getTaggerIdent().getWhen().getTime() / 1000);
            } catch (Exception e) {
                // Fallback to commit timestamp
            }
        }
        
        // Lightweight tag or fallback
        ObjectId commitId = tag.getPeeledObjectId() != null ? tag.getPeeledObjectId() : tag.getObjectId();
        RevCommit commit = revWalk.parseCommit(commitId);
        return Instant.ofEpochSecond(commit.getCommitTime());
    }
    
    public void close() {
        git.close();
        repository.close();
    }
}
