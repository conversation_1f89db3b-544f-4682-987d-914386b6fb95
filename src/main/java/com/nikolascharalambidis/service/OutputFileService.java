package com.nikolascharalambidis.service;

import com.nikolascharalambidis.model.CommitInfo;
import com.nikolascharalambidis.model.TagInfo;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for saving Git release log output to files.
 */
public class OutputFileService {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * Saves the Git release log to a text file.
     * 
     * @param tagInfos List of tag information
     * @param outputFile The output file
     * @throws IOException if file writing fails
     */
    public void saveToTextFile(List<TagInfo> tagInfos, File outputFile) throws IOException {
        try (FileWriter writer = new FileWriter(outputFile)) {
            writer.write("================================================================================\n");
            writer.write("GIT RELEASE LOG\n");
            writer.write("================================================================================\n\n");
            
            for (TagInfo tagInfo : tagInfos) {
                writeTagInfo(writer, tagInfo);
                writer.write("\n");
            }
            
            writer.write("================================================================================\n");
            writer.write("End of Git Release Log\n");
            writer.write("================================================================================\n");
            
            writeSummary(writer, tagInfos);
        }
    }
    
    /**
     * Saves the Git release log to a CSV file.
     * 
     * @param tagInfos List of tag information
     * @param outputFile The output CSV file
     * @throws IOException if file writing fails
     */
    public void saveToCsvFile(List<TagInfo> tagInfos, File outputFile) throws IOException {
        try (FileWriter writer = new FileWriter(outputFile)) {
            // CSV Header
            writer.write("Tag,TagDate,CommitHash,CommitMessage,Author,AuthorEmail,CommitDate,BranchName,Category\n");
            
            for (TagInfo tagInfo : tagInfos) {
                String tagName = escapeCsv(tagInfo.getName());
                String tagDate = tagInfo.getTimestamp() != null ? 
                        tagInfo.getTimestamp().atZone(ZoneId.systemDefault()).format(DATE_FORMATTER) : "";
                
                for (CommitInfo commit : tagInfo.getCommits()) {
                    writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                            tagName,
                            tagDate,
                            commit.getShortHash(),
                            escapeCsv(commit.getMessage()),
                            escapeCsv(commit.getAuthorName()),
                            escapeCsv(commit.getAuthorEmail()),
                            commit.getTimestamp().atZone(ZoneId.systemDefault()).format(DATE_FORMATTER),
                            escapeCsv(commit.getBranchName()),
                            escapeCsv(commit.getCategory())
                    ));
                }
            }
        }
    }
    
    /**
     * Saves category statistics to a text file.
     * 
     * @param tagInfos List of tag information
     * @param outputFile The output file
     * @throws IOException if file writing fails
     */
    public void saveCategoryStatistics(List<TagInfo> tagInfos, File outputFile) throws IOException {
        Map<String, Long> categoryStats = tagInfos.stream()
                .flatMap(tag -> tag.getCommits().stream())
                .collect(Collectors.groupingBy(
                        commit -> commit.getCategory() != null ? commit.getCategory() : "uncategorized",
                        Collectors.counting()
                ));
        
        try (FileWriter writer = new FileWriter(outputFile)) {
            writer.write("================================================================================\n");
            writer.write("COMMIT CATEGORY STATISTICS\n");
            writer.write("================================================================================\n\n");
            
            long totalCommits = categoryStats.values().stream().mapToLong(Long::longValue).sum();
            
            categoryStats.entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .forEach(entry -> {
                        try {
                            double percentage = (entry.getValue() * 100.0) / totalCommits;
                            writer.write(String.format("📊 %s: %d commits (%.1f%%)\n", 
                                    entry.getKey(), entry.getValue(), percentage));
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
            
            writer.write(String.format("\n📈 Total Commits: %d\n", totalCommits));
        }
    }
    
    private void writeTagInfo(FileWriter writer, TagInfo tagInfo) throws IOException {
        writer.write("📋 TAG: " + tagInfo.getName() + "\n");
        
        if (tagInfo.getTimestamp() != null) {
            String formattedDate = tagInfo.getTimestamp()
                    .atZone(ZoneId.systemDefault())
                    .format(DATE_FORMATTER);
            writer.write("📅 Date: " + formattedDate + "\n");
        }
        
        if (tagInfo.isAnnotated() && tagInfo.getMessage() != null && !tagInfo.getMessage().trim().isEmpty()) {
            writer.write("💬 Message: " + tagInfo.getMessage().trim() + "\n");
        }
        
        if (tagInfo.getTaggerName() != null) {
            writer.write("👤 Tagger: " + tagInfo.getTaggerName() + 
                    (tagInfo.getTaggerEmail() != null ? " <" + tagInfo.getTaggerEmail() + ">" : "") + "\n");
        }
        
        if (tagInfo.getTargetCommitHash() != null) {
            writer.write("🎯 Target Commit: " + tagInfo.getTargetCommitHash().substring(0, 7) + "\n");
        }
        
        writer.write("📊 Commits: " + tagInfo.getCommits().size() + "\n");
        writer.write("----------------------------------------\n");
        
        if (tagInfo.getCommits().isEmpty()) {
            writer.write("  No commits found for this tag.\n");
        } else {
            for (CommitInfo commit : tagInfo.getCommits()) {
                writeCommitInfo(writer, commit);
            }
        }
    }
    
    private void writeCommitInfo(FileWriter writer, CommitInfo commit) throws IOException {
        String formattedDate = commit.getTimestamp()
                .atZone(ZoneId.systemDefault())
                .format(DATE_FORMATTER);
        
        writer.write(String.format("  🔸 %s - %s", commit.getShortHash(), commit.getMessage()));
        
        if (commit.getBranchName() != null) {
            writer.write(String.format(" [%s]", commit.getBranchName()));
        }
        
        if (commit.getCategory() != null) {
            writer.write(String.format(" (%s)", commit.getCategory()));
        }
        
        writer.write("\n");
        
        writer.write(String.format("     👤 %s <%s> on %s\n", 
                commit.getAuthorName(), 
                commit.getAuthorEmail(), 
                formattedDate));
        
        // Show committer info if different from author
        if (!commit.getAuthorName().equals(commit.getCommitterName()) || 
            !commit.getAuthorEmail().equals(commit.getCommitterEmail())) {
            String committerDate = commit.getCommitterTimestamp()
                    .atZone(ZoneId.systemDefault())
                    .format(DATE_FORMATTER);
            writer.write(String.format("     🔧 Committed by %s <%s> on %s\n",
                    commit.getCommitterName(),
                    commit.getCommitterEmail(),
                    committerDate));
        }
        
        writer.write("\n");
    }
    
    private void writeSummary(FileWriter writer, List<TagInfo> tagInfos) throws IOException {
        if (tagInfos.isEmpty()) {
            return;
        }
        
        int totalTags = (int) tagInfos.stream().filter(tag -> !tag.getName().startsWith("Untagged") && !tag.getName().startsWith("All")).count();
        int totalCommits = tagInfos.stream().mapToInt(tag -> tag.getCommits().size()).sum();
        
        writer.write("📈 SUMMARY:\n");
        writer.write("   Total Tags: " + totalTags + "\n");
        writer.write("   Total Commits: " + totalCommits + "\n");
        
        if (totalTags > 0) {
            double avgCommitsPerTag = (double) totalCommits / totalTags;
            writer.write(String.format("   Average Commits per Tag: %.1f\n", avgCommitsPerTag));
        }
    }
    
    private String escapeCsv(String value) {
        if (value == null) {
            return "";
        }
        
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }
}
